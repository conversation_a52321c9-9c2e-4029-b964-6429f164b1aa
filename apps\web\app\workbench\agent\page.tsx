"use client";

import React, { useState, useEffect } from 'react';
import { AgentPage } from '@repo/ui/main/agent';
import { SiApachedolphinscheduler } from "react-icons/si";
import { useToast, ToastManager } from '@repo/ui/src/components/basic';

import { mcpConfigService } from '../../../src/services/mcpConfigService';
import { ConnectService } from '../../../src/services/connectService';
import { ConnectConfigService } from '../../../src/services/connectConfigService';
import { ChatService } from '../../../src/services/chatService';
import { getAllWorkflowConfigs, WorkflowConfigData } from '../../../src/services/workflowConfigService';
import { AgentConfig, McpConfig } from '@repo/common';
import { useChatStream } from '../../../src/hooks/useChatStream';

// 导入 AgentService 的类型
import { AgentData } from '../../../src/services/agentService';

// 类型转换函数：将 AgentData 转换为 AgentConfig
const convertAgentDataToConfig = (agentData: AgentData): AgentConfig => {
  return {
    id: agentData.id || '',
    name: agentData.name,
    description: agentData.description,
    prompt: agentData.prompt || undefined,
    avatar: agentData.avatar || undefined,
    connectid: agentData.connectid,
    modelId: agentData.modelId,
    modelName: agentData.modelName || undefined,
    createdAt: new Date(), // 使用当前时间作为默认值
    updatedAt: new Date(), // 使用当前时间作为默认值
    createUser: agentData.createUser || '',
    mcpTools: agentData.mcpTools,
    connectConfig: agentData.connectConfig ? {
      id: agentData.connectConfig.id,
      name: agentData.connectConfig.name,
      ctype: agentData.connectConfig.ctype,
      mtype: agentData.connectConfig.mtype || ''
    } : undefined
  };
};



export default function AgentPageContainer() {
  const [loading, setLoading] = useState(true);
  const [agents, setAgents] = useState<AgentConfig[]>([]);
  const [mcpConfigs, setMcpConfigs] = useState<McpConfig[]>([]);
  const [workflowConfigs, setWorkflowConfigs] = useState<WorkflowConfigData[]>([]);
  const toastHook = useToast();
  const { toasts, removeToast } = toastHook;

  // 使用 useChatStream hook 获取流式聊天相关数据
  const {
    messages: streamMessages,
    isLoading: streamIsLoading,
    threadId,
    isLoadingThread,
    sendMessage: onStreamSendMessage,
    loadThread: onLoadThread,
    setAgent: onSetAgent,
    startNewChat: onStartNewChat,
  } = useChatStream({
    endpoint: "/api/agentic",
    userId: "admin",
    agentId: null, // 初始为null，会在选择agent时动态设置
    agentName: "智能体",
    agentAvatar: "🤖",
  });

  // 获取智能体数据
  const handleFetchAgents = async (): Promise<AgentConfig[]> => {
    try {
      // 排除prompt字段以减少数据传输量
      const response = await fetch('/api/agents?excludeFields=prompt');
      const result = await response.json();
      
      if (result.success) {
        const agentsData: AgentData[] = result.data || [];
        const agentConfigs = agentsData.map(convertAgentDataToConfig);
        setAgents(agentConfigs);
        return agentConfigs;
      } else {
        setAgents([]);
        return [];
      }
    } catch (error) {
      console.error('获取智能体列表失败:', error);
      setAgents([]);
      return [];
    }
  };

  // 删除智能体
  const handleDeleteAgent = async (agentId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/agents/${agentId}`, {
        method: 'DELETE'
      });
      
      const result = await response.json();
      
      if (result.success) {
        // 重新获取智能体列表
        await handleFetchAgents();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('删除智能体失败:', error);
      return false;
    }
  };

  // 编辑智能体
  const handleEditAgent = (agent: AgentConfig) => {
    // 这里可以添加编辑逻辑，比如打开编辑模态框等
  };

  const handleMcpSave = async (data: any): Promise<boolean> => {
    try {
      const result = await mcpConfigService.saveMcpConfig(data);
      
      if (result.success) {
        // 重新获取MCP配置列表
        await handleFetchMcpConfigs();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  };

  // MCP删除回调函数
  const handleDeleteMcp = async () => {
    try {
      await handleFetchMcpConfigs();
    } catch (error) {
      console.error('❌ [page.tsx] MCP列表刷新失败:', error);
    }
  };

  const handleFetchLLMConnects = async () => {
    try {
      const response = await fetch('/api/connect/config?mtype=llm');
      const result = await response.json();
      
      if (result.success) {
        return result.data || [];
      } else {
        return [];
      }
    } catch (error) {
      return [];
    }
  };

  const handleFetchMcpConfigs = async () => {
    try {
      const response = await fetch('/api/mcp-configs');
      const result = await response.json();
      
      if (result.success) {
        const mcpData = result.data || [];
        setMcpConfigs(mcpData);
        return mcpData;
      } else {
        setMcpConfigs([]);
        return [];
      }
    } catch (error) {
      setMcpConfigs([]);
      return [];
    }
  };

  // 获取工作流配置
  const handleFetchWorkflowConfigs = async () => {
    try {
      const result = await getAllWorkflowConfigs();
      
      if (result.success) {
        const workflowData = result.data || [];
        setWorkflowConfigs(workflowData);
        return workflowData;
      } else {
        setWorkflowConfigs([]);
        return [];
      }
    } catch (error) {
      setWorkflowConfigs([]);
      return [];
    }
  };

  const handleAgentSave = async (data: any): Promise<boolean> => {
    try {
      const response = await fetch('/api/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      const result = await response.json();
      
      if (result.success) {
        // 重新获取智能体列表
        await handleFetchAgents();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  };

  // 获取连接列表的回调函数
  const handleFetchConnects = async () => {
    try {
      const result = await ConnectConfigService.getConnectConfigs({ mtype: 'rd-db' });
      
      if (result.success) {
        // 将连接配置数据转换为符合UI组件期望的结构
        const transformedData = (result.data || []).map((item: any) => ({
          id: item.id || item.ctype,
          name: item.name,
          ctype: item.ctype,
          description: item.description || `${item.ctype} 数据库连接`
        }));
        return transformedData;
      } else {
        throw new Error(result.error || '获取连接配置失败');
      }
    } catch (error) {
      console.error('❌ handleFetchConnects 错误:', error);
      throw error;
    }
  };

  // 获取连接详情的回调函数
  const handleFetchConnectDetails = async (connectId: string) => {
    try {
      const result = await ConnectService.getConnectDetail(connectId);
      
      if (result.success) {
        // 将平面数据结构转换为嵌套结构，以匹配 ConnectSettings 的期望
        const transformedData = {
          overview: {
            // 动态展开所有字段，避免遗漏新增字段
            ...result.data,
            // 确保detail中的字段不会覆盖overview
            fields: undefined,
            supportedModels: undefined,
            validateConnection: undefined,
            connectionTimeout: undefined
          },
          detail: {
            fields: result.data.fields || [],
            supportedModels: result.data.supportedModels || [],
            validateConnection: result.data.validateConnection ?? true,
            connectionTimeout: result.data.connectionTimeout
          }
        };
        return transformedData;
      } else {
        throw new Error('获取连接详情失败');
      }
    } catch (error) {
      console.error('❌ handleFetchConnectDetails 错误:', error);
      throw error;
    }
  };

  // 连接保存的回调函数
  const handleConnectSave = async (data: any) => {
    try {
      const result = await ConnectService.saveConnectConfig(data);
      return result.success;
    } catch (error) {
      console.error('❌ handleConnectSave 错误:', error);
      return false;
    }
  };

  // 连接测试的回调函数
  const handleConnectTest = async (config: Record<string, any>, message?: string) => {
    try {
      // 这里需要根据config中的connectId来调用测试
      const connectId = config.connectId || 'unknown';
      const result = await ConnectService.testConnection(connectId, config, message);
      return result;
    } catch (error) {
      console.error('❌ handleConnectTest 错误:', error);
      throw error;
    }
  };



  // 组件加载时获取数据
  useEffect(() => {
    const initData = async () => {
      setLoading(true);
      
      try {
        // 并行获取智能体、MCP配置和工作流配置数据
        await Promise.all([
          handleFetchAgents(),
          handleFetchMcpConfigs(),
          handleFetchWorkflowConfigs()
        ]);
      } catch (error) {
        console.error('数据初始化失败:', error);
      } finally {
        setLoading(false);
      }
    };

    initData();
  }, []);

  return (
    <>
      <AgentPage
        loading={loading}
        DocumentIcon={SiApachedolphinscheduler}
        title='智能体工坊'
        slogan='无需编写代码，通过直观的可视化界面快速配置和部署AI智能体。支持自定义知识库、MCP和交互逻辑，让每个人都能轻松打造专属AI助手。'
        agents={agents}
        mcpConfigs={mcpConfigs}
        workflowConfigs={workflowConfigs}
        onMcpSave={handleMcpSave}
        onDeleteMcp={handleDeleteMcp}
        onFetchLLMConnects={handleFetchLLMConnects}
        onFetchMcpConfigs={handleFetchMcpConfigs}
        onFetchWorkflowConfigs={handleFetchWorkflowConfigs}
        onFetchAgents={handleFetchAgents}
        onAgentSave={handleAgentSave}
        onDeleteAgent={handleDeleteAgent}
        onEditAgent={handleEditAgent}
        onFetchConnects={handleFetchConnects}
        onFetchConnectDetails={handleFetchConnectDetails}
        onConnectSave={handleConnectSave}
        onConnectTest={handleConnectTest}
        toastHook={toastHook}
        // 流式聊天相关 props
        streamMessages={streamMessages}
        streamIsLoading={streamIsLoading}
        threadId={threadId}
        isLoadingThread={isLoadingThread}
        onStreamSendMessage={onStreamSendMessage}
        onLoadThread={onLoadThread}
        onSetAgent={onSetAgent}
        onStartNewChat={onStartNewChat}
        userId="admin"
      />
      <ToastManager toasts={toasts} onRemove={removeToast} />
    </>
  );
}