import { NextRequest } from "next/server";
import { J<PERSON>NPath } from '@astronautlabs/jsonpath'
import { getWorkflowEngineConfigById, prisma } from "@repo/db";
import { AiAgent as AgentConfig } from "@repo/db/prisma/client";
import { agentThreadAdapter } from "@/loaders/AgentThreadAdapter";
import {
    functionManager,
    inngest,
    workflowRegister,
    schedulerConfig,
    triggerConfig,
    mcpManager,
    agentManager,
    AiAgent,
    ChatModel,
    ModelSeries,
    AgentAbilities, WorkflowReference, ConnectReference
} from "@repo/engine"
import { credentialManager, workflowConfigManager } from "@repo/common";
import { DefaultCredentialLoader, DefaultWorkflowLoader } from "@/loaders";

const config = {
    "id": "workflow-schedule-callback",
    "event": "workflow/schedule/callback",
}

const callback = inngest.createFunction(
    { id: config.id},
    { event: config.event },
    async ({ event, step }) => {
        const id = event.data.workflowId as string;
        const workflow = await getWorkflowEngineConfigById(id);
        return step.sendEvent("run", {
            name: triggerConfig.event,
            data: workflow
        });
    });

functionManager.add(callback.name, callback);

async function findScheduleNode(id: string) {
    const workflowEngineConfig = await getWorkflowEngineConfigById(id);
    const nodes = JSONPath.query(workflowEngineConfig.actions, "$[?(@.kind=='schedule')]");
    return nodes.length > 0 ? nodes[0] : null;
}

async function setScheduleEvent(id : string, cron : string, action: string) {
    return inngest.send({
        name: schedulerConfig.event,
        data: {
            cron: cron,
            action: action,
            callback: {
                event: config.event,
                data: {
                    workflowId: id
                }
            },
        }
    })
}

// hook db
prisma.$use(async (params, next) => {

    console.debug(`hook db: ${params.model}:${params.action}`);

    const result = await next(params);

    if(params.model === 'WorkflowConfig') {
        let action = null;
        let id = null;

        if (params.action === 'create') {
            if (result.isActive) {
                action = 'add';
                id = result.id;
            }
        } else if (params.action === 'update') {
            if (result.isActive) {
                action = 'add';
            } else {
                action = 'remove';
            }
            id = result.id;
        } else if (params.action === 'delete') {
            action = 'delete';
            id = params.args.data.id;
        }

        if (action != null) {

            const schedule = await findScheduleNode(id);
            if (schedule != null && schedule.inputs != null && schedule.inputs.cronExpression != null) {
                await setScheduleEvent(id, schedule.inputs.cronExpression, action);
            }
        }

        return result;
    } else if (params.model === 'AiMcp') {
        if (params.action === 'create') {
            mcpManager.add(JSON.parse(result.mcpinfo));
        } else if (params.action === 'update') {
            mcpManager.update(JSON.parse(result.mcpinfo));
        } else if (params.action === 'delete') {
            // 对于删除操作，ID在where条件中
            const deleteId = params.args.where?.id || params.args.data?.id;
            if (deleteId) {
                mcpManager.remove(deleteId);
            }
        }

        return result;
    } else if(params.model === 'AiAgent') {
        if (params.action === 'create') {
            agentManager.add(await loadAgent(result));
        } else if (params.action === 'update') {
            agentManager.update(await loadAgent(result));
        } else if (params.action === 'delete') {
            // 对于删除操作，ID在where条件中
            const deleteId = params.args.where?.id || params.args.data?.id;
            if (deleteId) {
                agentManager.delete(deleteId);
            }
        }
    }

    return result;
});

// init mcp servers
async function initMcpServers() {
    const mcpConfigs = await prisma.aiMcp.findMany();
    for (const mcpConfig of mcpConfigs) {
        mcpManager.add({
            ...JSON.parse(mcpConfig.mcpinfo),
            id: mcpConfig.id
        });
        console.debug(`MCP Server '${mcpConfig.name}' loaded.`);
    }
}

async function loadAgent(agentConfig: AgentConfig) : Promise<AiAgent> {

    const connConfig = await prisma.connectConfig.findUnique({
        where : {
            id: agentConfig.connectid
        }
    });

    const chatModel: ChatModel = {};

    if(connConfig) {
        const configData = JSON.parse(connConfig.configinfo);

        if(connConfig.mtype === "llm") {
            chatModel.series = configData.driver as ModelSeries;
            chatModel.model = agentConfig.modelId;
            chatModel.apiKey = configData.apiKey;
            chatModel.baseUrl = configData.baseUrl;
        }
    }

    function parseAbilities(info: string | null | undefined) : AgentAbilities | undefined {
        if(!info) {
            return undefined;
        }
        try {
            const abilities = JSON.parse(info) as unknown as AgentAbilities;
            if(abilities) {
                return abilities;
            }
            return undefined;
        } catch(err) {
            console.error(err);
            return undefined;
        }
    }

    const agent : AiAgent = {
        id: agentConfig.id,
        name: agentConfig.name,
        description: agentConfig.description,
        systemMessage: agentConfig.prompt??"",
        chatModel: chatModel,
        mcpServers: [],
        workflows: [],
        connects: [],
        abilities: parseAbilities(agentConfig.agentinfo)
    };

    // load agent mcps
    const mcps = await prisma.agentMcp.findMany({
        where : {
            agentId: agent.id
        }
    })??[];

    for (const mcp of mcps) {
        const mcpConfig = mcpManager.get(mcp.mcpId);
        if(mcpConfig) {
            agent.mcpServers?.push(mcpConfig);
        }
    }

    // load agent workflows
    const workflows = await prisma.agentWorkflow.findMany({
        where : {
            agentId: agent.id
        },
        select : {
            workflow: {
                select: {
                    'id':true, 'name':true
                }
            }
        }
    });
    agent.workflows?.push(...workflows?.flatMap((item) => ({
        id: item.workflow.id,
        name: item.workflow.name,
    }) as WorkflowReference));

    // load agent connects
    const connects = await prisma.agentConnect.findMany({
        where : {
            agentId: agent.id
        },
        select : {
            connect: {
                select: {
                    'id':true, 'name':true
                }
            }
        }
    });
    agent.connects?.push(...connects?.flatMap((item) => ({
        id: item.connect.id,
        name: item.connect.name,
    }) as ConnectReference));

    console.debug(`agent #${agent.name} loaded, data: ${JSON.stringify(agent)}`);

    return agent;
}

async function initAgents() {
    agentManager.setAdapters({
        agentThread: agentThreadAdapter
    });

    const agentConfigs : AgentConfig[] = await prisma.aiAgent.findMany()??[];
    for (const agentConfig of agentConfigs) {
        agentManager.add(await loadAgent(agentConfig));
    }
}

async function initContainers() {
    credentialManager.bind(DefaultCredentialLoader);
    workflowConfigManager.bind(DefaultWorkflowLoader);
}

async function init() {
    await initContainers();
    await initMcpServers();
    await initAgents();
}

init().catch(console.error);

export async function GET(expectedReq: NextRequest, res: unknown){
    return await workflowRegister().GET(expectedReq, res);
}

export async function POST(expectedReq: NextRequest, res: unknown){
    return await workflowRegister().POST(expectedReq, res);
}

export async function PUT(expectedReq: NextRequest, res: unknown){
    return await workflowRegister().PUT(expectedReq, res);
}