/**
 * 节点处理相关工具函数
 */

import { Edge } from 'reactflow';
import { NodeDetails, NodeValidationResult, NodeExecutionContext } from '../types/node';
import { Result, handleSyncOperation, logger } from './errorHandling';

/**
 * 递归获取所有前置节点
 * @param nodeId 目标节点ID
 * @param edges 边数据
 * @param nodesDetailsMap 节点详情映射
 * @param visited 已访问的节点集合，用于防止循环引用
 * @returns 所有前置节点ID数组
 */
export const getAllPreviousNodeIds = (
  nodeId: string,
  edges: Edge[],
  nodesDetailsMap: Record<string, NodeDetails>,
  visited: Set<string> = new Set()
): string[] => {
  // 防止循环引用
  if (visited.has(nodeId)) {
    logger.warn('检测到循环引用', { nodeId, visited: Array.from(visited) });
    return [];
  }
  visited.add(nodeId);

  // 获取直接前置节点
  const directPreviousNodeIds = edges
    .filter(edge => edge.target === nodeId)
    .map(edge => edge.source)
    .filter(sourceId => Object.keys(nodesDetailsMap).includes(sourceId));

  // 递归获取所有前置节点
  const allPreviousNodeIds = new Set<string>();

  for (const previousNodeId of directPreviousNodeIds) {
    // 添加直接前置节点
    allPreviousNodeIds.add(previousNodeId);

    // 递归获取间接前置节点
    const indirectPreviousNodeIds = getAllPreviousNodeIds(
      previousNodeId,
      edges,
      nodesDetailsMap,
      new Set(visited)
    );

    indirectPreviousNodeIds.forEach(id => allPreviousNodeIds.add(id));
  }

  logger.debug('获取前置节点完成', {
    nodeId,
    directCount: directPreviousNodeIds.length,
    totalCount: allPreviousNodeIds.size,
    previousNodes: Array.from(allPreviousNodeIds)
  });

  return Array.from(allPreviousNodeIds);
};

/**
 * 获取直接前置节点（只获取一层）
 */
export const getDirectPreviousNodeIds = (
  nodeId: string,
  edges: Edge[],
  nodesDetailsMap: Record<string, NodeDetails>
): string[] => {
  const directPreviousNodeIds = edges
    .filter(edge => edge.target === nodeId)
    .map(edge => edge.source)
    .filter(sourceId => Object.keys(nodesDetailsMap).includes(sourceId));

  logger.debug('获取直接前置节点', { nodeId, previousNodes: directPreviousNodeIds });
  
  return directPreviousNodeIds;
};

/**
 * 获取所有后续节点
 */
export const getAllNextNodeIds = (
  nodeId: string,
  edges: Edge[],
  nodesDetailsMap: Record<string, NodeDetails>,
  visited: Set<string> = new Set()
): string[] => {
  // 防止循环引用
  if (visited.has(nodeId)) {
    logger.warn('检测到循环引用', { nodeId, visited: Array.from(visited) });
    return [];
  }
  visited.add(nodeId);

  // 获取直接后续节点
  const directNextNodeIds = edges
    .filter(edge => edge.source === nodeId)
    .map(edge => edge.target)
    .filter(targetId => Object.keys(nodesDetailsMap).includes(targetId));

  // 递归获取所有后续节点
  const allNextNodeIds = new Set<string>();

  for (const nextNodeId of directNextNodeIds) {
    // 添加直接后续节点
    allNextNodeIds.add(nextNodeId);

    // 递归获取间接后续节点
    const indirectNextNodeIds = getAllNextNodeIds(
      nextNodeId,
      edges,
      nodesDetailsMap,
      new Set(visited)
    );

    indirectNextNodeIds.forEach(id => allNextNodeIds.add(id));
  }

  logger.debug('获取后续节点完成', {
    nodeId,
    directCount: directNextNodeIds.length,
    totalCount: allNextNodeIds.size,
    nextNodes: Array.from(allNextNodeIds)
  });

  return Array.from(allNextNodeIds);
};

/**
 * 查找工作流的起始节点（没有前置节点的节点）
 */
export const findStartNodes = (
  edges: Edge[],
  nodesDetailsMap: Record<string, NodeDetails>
): string[] => {
  const allNodeIds = Object.keys(nodesDetailsMap);
  const targetNodeIds = new Set(edges.map(edge => edge.target));
  
  const startNodes = allNodeIds.filter(nodeId => !targetNodeIds.has(nodeId));
  
  logger.debug('查找起始节点', { startNodes, totalNodes: allNodeIds.length });
  
  return startNodes;
};

/**
 * 查找工作流的结束节点（没有后续节点的节点）
 */
export const findEndNodes = (
  edges: Edge[],
  nodesDetailsMap: Record<string, NodeDetails>
): string[] => {
  const allNodeIds = Object.keys(nodesDetailsMap);
  const sourceNodeIds = new Set(edges.map(edge => edge.source));
  
  const endNodes = allNodeIds.filter(nodeId => !sourceNodeIds.has(nodeId));
  
  logger.debug('查找结束节点', { endNodes, totalNodes: allNodeIds.length });
  
  return endNodes;
};

/**
 * 验证节点配置的完整性
 */
export const validateNodeConfiguration = (
  nodeId: string,
  nodeDetails: NodeDetails
): Result<NodeValidationResult> => {
  return handleSyncOperation(() => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查基本信息
    if (!nodeDetails.nodeInfo) {
      errors.push('缺少节点信息');
    }

    if (!nodeDetails.originalNodeKind) {
      warnings.push('缺少原始节点类型');
    }

    // 检查保存的值
    if (!nodeDetails.savedValues || Object.keys(nodeDetails.savedValues).length === 0) {
      warnings.push('节点未配置任何参数');
    }

    // 检查必填字段（这里可以根据具体的节点类型进行扩展）
    const nodeInfo = nodeDetails.nodeInfo;
    if (nodeInfo?.data?.required) {
      const requiredFields = nodeInfo.data.required;
      const savedValues = nodeDetails.savedValues || {};
      
      for (const field of requiredFields) {
        if (!savedValues[field] || savedValues[field] === '') {
          errors.push(`必填字段 "${field}" 未配置`);
        }
      }
    }

    const result: NodeValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings
    };

    logger.debug('节点配置验证完成', { nodeId, result });

    return result;
  }, `验证节点${nodeId}配置失败`);
};

/**
 * 检查节点是否可以执行
 */
export const canNodeExecute = (
  nodeId: string,
  nodeDetails: NodeDetails,
  previousNodeIds: string[],
  nodesTestResultsMap: Record<string, any>
): boolean => {
  // 检查节点本身是否配置完整
  const validationResult = validateNodeConfiguration(nodeId, nodeDetails);
  if (!validationResult.success || !validationResult.data.isValid) {
    logger.warn('节点配置不完整，无法执行', { nodeId, validation: validationResult });
    return false;
  }

  // 检查前置节点是否都已执行成功
  for (const prevNodeId of previousNodeIds) {
    const testResult = nodesTestResultsMap[prevNodeId];
    if (!testResult || !testResult.success) {
      logger.warn('前置节点未成功执行，无法执行当前节点', { 
        nodeId, 
        prevNodeId, 
        hasResult: !!testResult,
        success: testResult?.success 
      });
      return false;
    }
  }

  return true;
};

/**
 * 构建节点执行上下文
 */
export const buildNodeExecutionContext = (
  nodeId: string,
  nodeDetails: NodeDetails,
  previousNodeIds: string[],
  nodesTestResultsMap: Record<string, any>
): Result<NodeExecutionContext> => {
  return handleSyncOperation(() => {
    const context: NodeExecutionContext = {
      nodeId,
      inputs: nodeDetails.savedValues || {},
      previousNodes: previousNodeIds,
      testResultsMap: nodesTestResultsMap
    };

    logger.debug('构建节点执行上下文', { nodeId, context });

    return context;
  }, `构建节点${nodeId}执行上下文失败`);
};

/**
 * 提取节点输入中的模板变量
 */
export const extractTemplateVariables = (inputs: Record<string, any>): string[] => {
  const inputsString = JSON.stringify(inputs);
  const templateVariableRegex = /\{\{\s*\$\.([^.\s}]+)/g;
  const directNodeIdRegex = /\$\.([^.\s}]+)\.?/g;

  const templateNodeIds: string[] = [];
  const directNodeIds: string[] = [];
  let match;

  while ((match = templateVariableRegex.exec(inputsString)) !== null) {
    templateNodeIds.push(match[1]);
  }
  while ((match = directNodeIdRegex.exec(inputsString)) !== null) {
    directNodeIds.push(match[1]);
  }

  const allExtractedNodeIds = [...new Set([...templateNodeIds, ...directNodeIds])];
  
  logger.debug('提取模板变量', { 
    inputs: Object.keys(inputs),
    templateNodeIds,
    directNodeIds,
    allExtractedNodeIds
  });

  return allExtractedNodeIds;
};

/**
 * 检查是否存在循环依赖
 */
export const detectCircularDependency = (
  edges: Edge[],
  nodesDetailsMap: Record<string, NodeDetails>
): Result<string[]> => {
  return handleSyncOperation(() => {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const cycles: string[] = [];

    const dfs = (nodeId: string, path: string[]): boolean => {
      if (recursionStack.has(nodeId)) {
        const cycleStart = path.indexOf(nodeId);
        const cycle = path.slice(cycleStart).concat(nodeId);
        cycles.push(cycle.join(' -> '));
        return true;
      }

      if (visited.has(nodeId)) {
        return false;
      }

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const nextNodes = edges
        .filter(edge => edge.source === nodeId)
        .map(edge => edge.target)
        .filter(targetId => Object.keys(nodesDetailsMap).includes(targetId));

      for (const nextNode of nextNodes) {
        if (dfs(nextNode, [...path, nodeId])) {
          // 找到循环，但继续检查其他路径
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    // 从所有节点开始检查
    for (const nodeId of Object.keys(nodesDetailsMap)) {
      if (!visited.has(nodeId)) {
        dfs(nodeId, []);
      }
    }

    logger.debug('循环依赖检测完成', { cycles });

    return cycles;
  }, '检测循环依赖失败');
};