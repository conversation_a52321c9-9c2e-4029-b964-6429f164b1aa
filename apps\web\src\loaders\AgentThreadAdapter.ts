import {
    type HistoryConfig,
    type History,
    type StateData,
    AgentResult,
    type TextMessage,
} from "@repo/engine";
import { PrismaClient } from "@prisma/client";
import { prisma } from "@repo/db";

export interface DbHistoryConfig {
    connectionString: string;
}

export class AgentHistoryAdapter<T extends StateData>
    implements HistoryConfig<T> {

    readonly #client: PrismaClient;

    constructor(client: PrismaClient) {
        this.#client = client;
    }

    // Table names with proper schema and prefix
    get tableNames() {
        return {
            threads: (prisma.agentThread as any).getTableName(),
            messages: (prisma.agentMessage as any).getTableName(),
        };
    }

    /**
     * Create a new conversation thread.
     */
    createThread = async (
        {state, step}: History.CreateThreadContext<T>
    ): Promise<{ threadId: string }> => {
        const client = this.#client;

        const operation = async () => {
            const result = await client.agentThread.create({
                data: {
                    agentId: state.data.agentId,
                    userId: state.data.userId || null,
                    metadata: JSON.stringify(state.data)
                }
            });
            return result.id;
        };

        const threadId = step
            ? await step.run("create-thread", operation)
            : await operation();

        console.log(`🆕 Created new thread: ${threadId}`);
        return {threadId};
    };

    /**
     * Load conversation history from storage.
     *
     * Returns complete conversation context including both user messages and agent results.
     * User messages are converted to fake AgentResults (agentName: "user") to maintain
     * consistency with the client-side pattern and preserve conversation continuity.
     */
    get = async ({threadId, step}: History.Context<T>): Promise<AgentResult[]> => {
        if (!threadId) {
            console.log("No threadId provided, returning empty history");
            return [];
        }

        const client = this.#client;

        const operation = async () => {
            // Load complete conversation history (both user messages and agent results)
            const result = await client.agentMessage.findMany({
                where: {threadId: threadId},
                orderBy: {createdAt: 'desc'},
                take: 5,
            });

            const conversationResults: AgentResult[] = [];

            for (const row of result) {
                if (row.messageType === 'user') {
                    // Convert user message to fake AgentResult (matching UI pattern)
                    const userMessage: TextMessage = {
                        type: "text",
                        role: "user",
                        content: row.content ?? "",
                        stop_reason: "stop"
                    };

                    const fakeUserResult = new AgentResult(
                        "user", // agentName: "user" (matches UI pattern)
                        [userMessage], // output contains the user message
                        [], // no tool calls for user messages
                        new Date(row.createdAt)
                    );

                    conversationResults.push(fakeUserResult);
                } else if (row.messageType === 'agent') {
                    // Deserialize real AgentResult objects from JSONB
                    const data = JSON.parse(row.data ?? "{}");
                    if(data.output?.length > 0 && data.output[0].type === "text") {
                        const realAgentResult = new AgentResult(
                            data.agentName,
                            data.output,
                            data.toolCalls,
                            new Date(data.createdAt)
                        );

                        conversationResults.push(realAgentResult);
                    }
                }
            }

            return conversationResults.reverse();
        };

        const results = step
            ? ((await step.run(
                "load-complete-history",
                operation
            )) as unknown as AgentResult[])
            : await operation();

        return results;
    };

    /**
     * Save new conversation results to storage.
     */
    appendResults = async ({
                               threadId,
                               newResults,
                               userMessage,
                               step,
                           }: History.Context<T> & {
        newResults: AgentResult[];
        userMessage?: {
            content: string;
            role: "user";
            timestamp: Date;
        };
    }): Promise<void> => {
        if (!threadId) {
            console.log("No threadId provided, skipping save");
            return;
        }

        if (!newResults?.length && !userMessage) {
            console.log("No newResults or userMessage provided, skipping save");
            return;
        }

        const client = this.#client;

        try {
            const operation = async () => {

                await client.$transaction(async (tx) => {

                    console.log("Updating thread timestamp...");

                    await tx.agentThread.update({
                        where: {id: threadId},
                        data: {
                            updatedAt: new Date(),
                        }
                    })

                    // Insert user message if provided
                    if (userMessage) {
                        console.log("Inserting user message...");
                        const userChecksum = `user_${userMessage.timestamp.getTime()}_${userMessage.content.substring(
                            0,
                            50
                        )}`;

                        await tx.agentMessage.create({
                            data: {
                                threadId: threadId,
                                messageType: "user",
                                content: userMessage.content,
                                checksum: userChecksum,
                                createdAt: userMessage.timestamp
                            }
                        });

                        console.log("User message inserted successfully");
                    }

                    // Insert agent results
                    if (newResults?.length > 0) {
                        console.log("Inserting agent messages...");
                        for (const result of newResults) {
                            const exportedData = result.export();

                            await tx.agentMessage.create({
                                data: {
                                    threadId: threadId,
                                    messageType: "agent",
                                    agentName: result.agentName,
                                    data: JSON.stringify(exportedData),
                                    raw: result.raw,
                                    checksum: result.checksum,
                                }
                            });
                            console.log(`Agent message inserted successfully`);
                        }
                    }

                    const totalSaved = (userMessage ? 1 : 0) + (newResults?.length || 0);
                    console.log(
                        `💾 Saved ${totalSaved} messages to thread ${threadId} (${
                            userMessage ? "1 user + " : ""
                        }${newResults?.length || 0} agent)`
                    );
                }, {
                    timeout: 50000
                })
            };

            step ? await step.run("save-results", operation) : await operation();
        } catch (error) {
            console.error("❌ appendResults failed:", error);
            throw error;
        }
    };
}

export const agentThreadAdapter = new AgentHistoryAdapter(prisma);