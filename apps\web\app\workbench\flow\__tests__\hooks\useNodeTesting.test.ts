/**
 * useNodeTesting hook 测试
 */

import { renderHook, act } from '@testing-library/react';
import { useNodeTesting } from '../../hooks/useNodeTesting';

// Mock services
jest.mock('@/services/nodeDebugService', () => ({
  debugNode: jest.fn()
}));

jest.mock('@/services/workflowTestService', () => ({
  testWorkflow: jest.fn()
}));

import { debugNode } from '@/services/nodeDebugService';
import { testWorkflow } from '@/services/workflowTestService';

// Mock数据
const mockProps = {
  workflowId: 'test-workflow',
  nodesDetailsMap: {
    node1: {
      nodeInfo: {
        id: 'node1',
        data: { kind: 'trigger', name: '触发器' },
        position: { x: 0, y: 0 },
        type: 'triggerNode'
      },
      savedValues: { param1: 'value1' },
      originalNodeKind: 'trigger'
    },
    node2: {
      nodeInfo: {
        id: 'node2',
        data: { kind: 'action', name: '动作' },
        position: { x: 100, y: 0 },
        type: 'actionNode'
      },
      savedValues: { param2: 'value2' },
      originalNodeKind: 'action'
    }
  },
  nodesTestResultsMap: {
    node1: {
      success: true,
      data: { result: 'ok' },
      timestamp: Date.now(),
      nodeId: 'node1'
    }
  },
  edgesState: [
    { id: 'edge1', source: 'node1', target: 'node2' }
  ],
  updateNodeTestResult: jest.fn(),
  updateNodeDetails: jest.fn(),
  showError: jest.fn(),
  showSuccess: jest.fn(),
  showWarning: jest.fn()
};

describe('useNodeTesting', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该初始化正确的状态', () => {
    const { result } = renderHook(() => useNodeTesting(mockProps));

    expect(result.current.isTestingWorkflow).toBe(false);
    expect(result.current.testingNodes).toEqual([]);
    expect(typeof result.current.handleNodeTest).toBe('function');
    expect(typeof result.current.handleWorkflowTest).toBe('function');
  });

  it('应该处理节点测试', async () => {
    const mockDebugResult = { success: true, data: { output: 'test result' } };
    (debugNode as jest.Mock).mockResolvedValueOnce(mockDebugResult);

    const { result } = renderHook(() => useNodeTesting(mockProps));

    await act(async () => {
      await result.current.handleNodeTest({ param1: 'test' }, 'node1');
    });

    expect(debugNode).toHaveBeenCalledWith(expect.objectContaining({
      actions: expect.arrayContaining([
        expect.objectContaining({
          id: 'node1',
          inputs: expect.objectContaining({ param1: 'test' }),
          kind: 'trigger'
        })
      ]),
      edges: expect.any(Array)
    }));

    expect(mockProps.updateNodeTestResult).toHaveBeenCalledWith('node1', expect.objectContaining({
      success: true,
      data: mockDebugResult,
      nodeId: 'node1'
    }));

    expect(mockProps.showSuccess).toHaveBeenCalled();
  });

  it('应该处理节点测试失败', async () => {
    const mockError = new Error('测试失败');
    (debugNode as jest.Mock).mockRejectedValueOnce(mockError);

    const { result } = renderHook(() => useNodeTesting(mockProps));

    await act(async () => {
      await result.current.handleNodeTest({ param1: 'test' }, 'node1');
    });

    expect(mockProps.updateNodeTestResult).toHaveBeenCalledWith('node1', expect.objectContaining({
      success: false,
      error: '节点 node1 测试失败',
      nodeId: 'node1'
    }));

    expect(mockProps.showError).toHaveBeenCalled();
  });

  it('应该处理工作流测试', async () => {
    const mockTestResult = { success: true, data: 'workflow result' };
    (testWorkflow as jest.Mock).mockResolvedValueOnce(mockTestResult);

    const { result } = renderHook(() => useNodeTesting(mockProps));

    await act(async () => {
      await result.current.handleWorkflowTest();
    });

    expect(testWorkflow).toHaveBeenCalledWith('test-workflow');
    expect(mockProps.showSuccess).toHaveBeenCalledWith(
      '成功',
      expect.stringContaining('工作流测试成功')
    );
  });

  it('应该处理工作流测试失败', async () => {
    const mockError = new Error('工作流测试失败');
    (testWorkflow as jest.Mock).mockRejectedValueOnce(mockError);

    const { result } = renderHook(() => useNodeTesting(mockProps));

    await act(async () => {
      await result.current.handleWorkflowTest();
    });

    expect(mockProps.showError).toHaveBeenCalledWith('出错:', '工作流测试失败');
  });

  it('应该防止重复测试同一节点', async () => {
    (debugNode as jest.Mock).mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ success: true }), 100))
    );

    const { result } = renderHook(() => useNodeTesting(mockProps));

    // 同时触发两次测试
    act(() => {
      result.current.handleNodeTest({ param1: 'test' }, 'node1');
      result.current.handleNodeTest({ param1: 'test' }, 'node1');
    });

    expect(mockProps.showWarning).toHaveBeenCalledWith(
      '测试进行中',
      '该节点正在测试中，请稍候'
    );

    // 等待异步操作完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 150));
    });

    // 应该只调用一次debugNode
    expect(debugNode).toHaveBeenCalledTimes(1);
  });

  it('应该处理缺少节点详情的情况', async () => {
    const { result } = renderHook(() => useNodeTesting(mockProps));

    await act(async () => {
      await result.current.handleNodeTest({ param1: 'test' }, 'nonexistent-node');
    });

    expect(mockProps.showError).toHaveBeenCalledWith(
      '节点错误',
      expect.stringContaining('找不到对应的节点')
    );

    expect(debugNode).not.toHaveBeenCalled();
  });

  it('应该保存模拟测试数据', () => {
    const { result } = renderHook(() => useNodeTesting(mockProps));

    const mockData = { result: 'mock data' };

    act(() => {
      result.current.handleSaveMockData(mockData, 'node1');
    });

    expect(mockProps.updateNodeTestResult).toHaveBeenCalledWith('node1', expect.objectContaining({
      success: true,
      data: mockData,
      nodeId: 'node1'
    }));

    expect(mockProps.showSuccess).toHaveBeenCalledWith('成功', '模拟测试数据已保存');
  });

  it('应该检查节点是否可以测试', () => {
    const { result } = renderHook(() => useNodeTesting(mockProps));

    // node1应该可以测试（没有前置节点）
    expect(result.current.canTestNode('node1')).toBe(true);

    // node2应该可以测试（前置节点node1有成功的测试结果）
    expect(result.current.canTestNode('node2')).toBe(true);

    // 不存在的节点不能测试
    expect(result.current.canTestNode('nonexistent')).toBe(false);
  });

  it('应该获取节点测试状态', () => {
    const { result } = renderHook(() => useNodeTesting(mockProps));

    const status = result.current.getNodeTestStatus('node1');

    expect(status.isTesting).toBe(false);
    expect(status.hasResult).toBe(true);
    expect(status.isSuccess).toBe(true);
    expect(status.data).toEqual({ result: 'ok' });
  });
});