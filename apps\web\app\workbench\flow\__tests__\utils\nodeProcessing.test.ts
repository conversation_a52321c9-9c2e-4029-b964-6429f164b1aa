/**
 * 节点处理工具函数测试
 */

import { 
  getAllPreviousNodeIds,
  getDirectPreviousNodeIds,
  getAllNextNodeIds,
  findStartNodes,
  findEndNodes,
  validateNodeConfiguration,
  canNodeExecute,
  extractTemplateVariables,
  detectCircularDependency
} from '../../utils/nodeProcessing';

// 模拟数据
const mockEdges = [
  { id: 'e1-2', source: 'node1', target: 'node2' },
  { id: 'e2-3', source: 'node2', target: 'node3' },
  { id: 'e2-4', source: 'node2', target: 'node4' },
  { id: 'e3-5', source: 'node3', target: 'node5' },
  { id: 'e4-5', source: 'node4', target: 'node5' }
];

const mockNodesDetailsMap = {
  node1: {
    nodeInfo: { id: 'node1', data: { kind: 'trigger' } },
    savedValues: { param1: 'value1' },
    originalNodeKind: 'trigger'
  },
  node2: {
    nodeInfo: { id: 'node2', data: { kind: 'action' } },
    savedValues: { param2: 'value2' },
    originalNodeKind: 'action'
  },
  node3: {
    nodeInfo: { id: 'node3', data: { kind: 'action' } },
    savedValues: { param3: 'value3' },
    originalNodeKind: 'action'
  },
  node4: {
    nodeInfo: { id: 'node4', data: { kind: 'action' } },
    savedValues: { param4: 'value4' },
    originalNodeKind: 'action'
  },
  node5: {
    nodeInfo: { id: 'node5', data: { kind: 'action' } },
    savedValues: { param5: 'value5' },
    originalNodeKind: 'action'
  }
};

const mockNodesTestResultsMap = {
  node1: { success: true, data: { result: 'ok' }, timestamp: Date.now(), nodeId: 'node1' },
  node2: { success: true, data: { result: 'ok' }, timestamp: Date.now(), nodeId: 'node2' },
  node3: { success: true, data: { result: 'ok' }, timestamp: Date.now(), nodeId: 'node3' },
  node4: { success: false, error: 'Failed', timestamp: Date.now(), nodeId: 'node4' }
};

describe('节点处理工具函数', () => {
  describe('getAllPreviousNodeIds', () => {
    it('应该返回所有前置节点', () => {
      const result = getAllPreviousNodeIds('node5', mockEdges, mockNodesDetailsMap);
      
      // node5的前置节点是node3和node4，node3的前置节点是node2，node2的前置节点是node1
      expect(result).toContain('node3');
      expect(result).toContain('node4');
      expect(result).toContain('node2');
      expect(result).toContain('node1');
      expect(result.length).toBe(4);
    });

    it('应该处理没有前置节点的情况', () => {
      const result = getAllPreviousNodeIds('node1', mockEdges, mockNodesDetailsMap);
      
      expect(result.length).toBe(0);
    });

    it('应该处理循环依赖的情况', () => {
      const cycleEdges = [
        ...mockEdges,
        { id: 'e5-2', source: 'node5', target: 'node2' } // 创建循环
      ];
      
      const result = getAllPreviousNodeIds('node5', cycleEdges, mockNodesDetailsMap);
      
      // 应该能处理循环依赖而不会无限递归
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('getDirectPreviousNodeIds', () => {
    it('应该只返回直接前置节点', () => {
      const result = getDirectPreviousNodeIds('node5', mockEdges, mockNodesDetailsMap);
      
      expect(result).toContain('node3');
      expect(result).toContain('node4');
      expect(result.length).toBe(2);
      
      // 不应该包含间接前置节点
      expect(result).not.toContain('node2');
      expect(result).not.toContain('node1');
    });
  });

  describe('findStartNodes', () => {
    it('应该找到起始节点', () => {
      const result = findStartNodes(mockEdges, mockNodesDetailsMap);
      
      expect(result).toContain('node1');
      expect(result.length).toBe(1);
    });
  });

  describe('findEndNodes', () => {
    it('应该找到结束节点', () => {
      const result = findEndNodes(mockEdges, mockNodesDetailsMap);
      
      expect(result).toContain('node5');
      expect(result.length).toBe(1);
    });
  });

  describe('extractTemplateVariables', () => {
    it('应该提取模板变量', () => {
      const inputs = {
        param1: 'normal value',
        param2: '{{$.node1.data}}',
        param3: 'prefix {{$.node2.result}} suffix',
        param4: '{{$.node1.data}} and {{$.node3.data}}'
      };
      
      const result = extractTemplateVariables(inputs);
      
      expect(result).toContain('node1');
      expect(result).toContain('node2');
      expect(result).toContain('node3');
      expect(result.length).toBe(3);
    });
  });

  describe('detectCircularDependency', () => {
    it('应该检测到循环依赖', () => {
      const cycleEdges = [
        ...mockEdges,
        { id: 'e5-2', source: 'node5', target: 'node2' } // 创建循环
      ];
      
      const result = detectCircularDependency(cycleEdges, mockNodesDetailsMap);
      
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.length).toBeGreaterThan(0);
      }
    });

    it('应该处理没有循环依赖的情况', () => {
      const result = detectCircularDependency(mockEdges, mockNodesDetailsMap);
      
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.length).toBe(0);
      }
    });
  });
});