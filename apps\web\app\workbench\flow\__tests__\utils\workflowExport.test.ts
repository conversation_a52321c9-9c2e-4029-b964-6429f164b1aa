/**
 * 工作流导出工具函数测试
 */

import { 
  exportWorkflowData, 
  copyWorkflowToClipboard, 
  exportWorkflowToFile,
  getCurrentCanvasData,
  validateWorkflowData
} from '../../utils/workflowExport';
import { WorkflowData } from '../../types/workflow';

// 模拟数据
const mockNodes = [
  {
    id: 'node1',
    type: 'triggerNode',
    position: { x: 100, y: 100 },
    data: {
      kind: 'trigger',
      name: '触发器',
      description: '触发器节点',
      icon: 'trigger.svg',
      category: 'trigger',
      categories: ['trigger'],
      version: '1.0.0',
      link: {
        inputs: [],
        outputs: [{ desc: '输出' }]
      },
      nodeWidth: 200
    }
  },
  {
    id: 'node2',
    type: 'actionNode',
    position: { x: 300, y: 100 },
    data: {
      kind: 'action',
      name: '动作',
      description: '动作节点',
      icon: 'action.svg',
      category: 'action',
      categories: ['action'],
      version: '1.0.0',
      link: {
        inputs: [{ desc: '输入' }],
        outputs: [{ desc: '输出' }]
      },
      nodeWidth: 200
    }
  }
];

const mockEdges = [
  {
    id: 'edge1',
    source: 'node1',
    target: 'node2',
    sourceHandle: 'right-0',
    targetHandle: 'left-0'
  }
];

const mockNodesDetailsMap = {
  node1: {
    nodeInfo: mockNodes[0],
    savedValues: { param1: 'value1' },
    originalNodeKind: 'trigger'
  },
  node2: {
    nodeInfo: mockNodes[1],
    savedValues: { param2: 'value2' },
    originalNodeKind: 'action'
  }
};

describe('工作流导出工具函数', () => {
  describe('exportWorkflowData', () => {
    it('应该正确导出工作流数据', () => {
      const result = exportWorkflowData({
        currentEdges: mockEdges,
        nodes: mockNodes,
        edges: mockEdges,
        nodesDetailsMap: mockNodesDetailsMap,
        workflowName: '测试工作流',
        workflowId: 'test-workflow'
      });

      expect(result.success).toBe(true);
      if (result.success) {
        const data = result.data;
        expect(data.metadata.workflowName).toBe('测试工作流');
        expect(data.metadata.workflowId).toBe('test-workflow');
        expect(data.nodes.length).toBe(2);
        expect(data.edges.length).toBe(1);
        
        // 验证节点数据
        expect(data.nodes[0].id).toBe('node1');
        expect(data.nodes[0].kind).toBe('trigger');
        expect(data.nodes[0].inputs).toEqual({ param1: 'value1' });
        
        // 验证边数据
        expect(data.edges[0].source).toBe('node1');
        expect(data.edges[0].target).toBe('node2');
      }
    });

    it('应该处理空节点和边的情况', () => {
      const result = exportWorkflowData({
        currentEdges: [],
        nodes: [],
        edges: [],
        nodesDetailsMap: {},
        workflowName: '空工作流',
        workflowId: 'empty-workflow'
      });

      expect(result.success).toBe(true);
      if (result.success) {
        const data = result.data;
        expect(data.metadata.workflowName).toBe('空工作流');
        expect(data.nodes.length).toBe(0);
        expect(data.edges.length).toBe(0);
      }
    });
  });

  describe('getCurrentCanvasData', () => {
    it('应该返回当前画布数据', () => {
      const result = getCurrentCanvasData(mockNodes, mockEdges);
      
      expect(result.nodes).toBe(mockNodes);
      expect(result.edges).toBe(mockEdges);
    });
  });

  describe('validateWorkflowData', () => {
    it('应该验证有效的工作流数据', () => {
      const validData: WorkflowData = {
        metadata: {
          workflowName: '有效工作流',
          workflowId: 'valid-workflow',
          exportedAt: new Date().toISOString(),
          version: '1.0.0'
        },
        nodes: [
          {
            id: 'node1',
            kind: 'trigger',
            type: 'triggerNode',
            position: { x: 100, y: 100 },
            inputs: {},
            data: {
              kind: 'trigger',
              name: '触发器',
              description: '触发器节点',
              icon: 'trigger.svg',
              category: 'trigger',
              categories: ['trigger'],
              version: '1.0.0',
              link: null
            }
          }
        ],
        edges: []
      };

      const result = validateWorkflowData(validData);
      expect(result.success).toBe(true);
    });

    it('应该检测到无效的工作流数据', () => {
      const invalidData = {
        metadata: {
          workflowName: '无效工作流'
          // 缺少必要字段
        },
        // 缺少nodes和edges
      } as any;

      const result = validateWorkflowData(invalidData);
      expect(result.success).toBe(false);
    });
  });
});