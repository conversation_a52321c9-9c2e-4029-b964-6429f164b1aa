// Workflow related interfaces
export const WorkflowDef = {
    identifier: "IWorkflow"
}

// 工作流配置接口
export interface WorkflowConfig {
  id: string;
  name: string;
  isActive: boolean;
  createdTime: string;
  updatedTime: string;
  createUser: string;
}

export interface WorkflowData {
    id: string;
    config?: any;
}

export interface WorkflowListOptions {
    limit?: number;
}

export interface IWorkflowLoader {
    get(id: string): Promise<WorkflowData | null | undefined>;
    list(opts?: WorkflowListOptions) : Promise<WorkflowData[] | null  | undefined>;
}