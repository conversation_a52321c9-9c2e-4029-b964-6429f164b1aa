/**
 * 工作流编辑器组件 - 应用层组件，集成UI组件和业务逻辑
 */

import React, { useCallback, useRef, useEffect, useState, useMemo } from 'react';
import { ReactFlowProvider, useNodesState, useEdgesState, Node, Edge } from 'reactflow';
import styled, { useTheme } from 'styled-components';

// UI组件层导入
import { NodeDetailsView } from '@repo/ui/src/node/NodeDetailsView';

// 应用层组件导入
import { WorkflowCanvas } from './WorkflowCanvas';
import { NodeMenu } from '../NodeManagement/NodeMenu';

// 应用层hooks导入
import { useNodeTesting } from '../../hooks/useNodeTesting';
import { useCanvasOperations } from '../../hooks/useCanvasOperations';
import { useConnectConfig } from '../../hooks/useConnectConfig';

// 工具函数导入
import { getAllPreviousNodeIds } from '../../utils/nodeProcessing';
import { ErrorBoundary } from '../ErrorBoundary';

// 类型导入
import { NodeDetails, FetchTablesResponse, ConnectConfig } from '../../types/node';

// Context导入
import { useWorkflow } from '@/contexts/WorkflowContext';

const WorkflowEditorContainer = styled.div`
  display: flex;
  height: 100%;
  width: 100%;
  position: relative;
`;

const CanvasContainer = styled.div`
  flex: 1;
  min-width: 0;
`;

interface WorkflowEditorProps {
  onMenuCollapseChange: (collapsed: boolean) => void;
  showError: (title: string, message: string) => void;
  showWarning: (title: string, message: string) => void;
  onCanvasStateChange?: (nodes: Node[], edges: Edge[]) => void;
  onFetchConnectConfigs?: (ctype?: string) => Promise<ConnectConfig[]>;
  onFetchTables?: (datasourceId: string, search?: string) => Promise<FetchTablesResponse>;
}

export const WorkflowEditor: React.FC<WorkflowEditorProps> = ({
  onMenuCollapseChange,
  showError,
  showWarning,
  onCanvasStateChange,
  onFetchConnectConfigs,
  onFetchTables
}) => {
  const theme = useTheme();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  // 工作流上下文
  const {
    nodes,
    edges,
    workflowId,
    nodesDetailsMap,
    nodesTestResultsMap,
    updateNodeDetails,
    updateNodeTestResult,
    deleteNodeCompletely
  } = useWorkflow();

  // 本地状态管理
  const [nodesState, setNodesState, onNodesChange] = useNodesState(nodes);
  const [edgesState, setEdgesState, onEdgesChange] = useEdgesState(edges);
  const [selectedNodeDetails, setSelectedNodeDetails] = useState<any>(null);
  const [nodeTestOutput, setNodeTestOutput] = useState<string>('');
  const [menuCollapsed, setMenuCollapsed] = useState(false);

  // 工作流ID变化时重置状态
  const [lastWorkflowId, setLastWorkflowId] = useState<string | null>(null);

  useEffect(() => {
    if (workflowId && workflowId !== lastWorkflowId) {
      setNodesState([]);
      setEdgesState([]);
      setLastWorkflowId(workflowId);
    }
  }, [workflowId, lastWorkflowId, setNodesState, setEdgesState]);

  // 同步Context数据到ReactFlow状态
  useEffect(() => {
    if (nodes.length > 0 && nodesState.length === 0) {
      setNodesState(nodes);
    }

    if (edges.length > 0 && edgesState.length === 0) {
      setEdgesState(edges);
    }

    // 检测工作流切换
    if (nodes.length > 0 && nodesState.length > 0) {
      const contextNodeIds = new Set(nodes.map(n => n.id));
      const reactFlowNodeIds = new Set(nodesState.map(n => n.id));
      const hasCommonNodes = [...contextNodeIds].some(id => reactFlowNodeIds.has(id));

      if (!hasCommonNodes) {
        setNodesState(nodes);
        setEdgesState(edges);
      }
    }
  }, [nodes, edges, nodesState.length, edgesState.length, setNodesState, setEdgesState]);

  // 初始化hooks
  const nodeTestingHook = useNodeTesting({
    workflowId: workflowId || '',
    nodesDetailsMap,
    nodesTestResultsMap,
    edgesState,
    updateNodeTestResult,
    updateNodeDetails,
    showError,
    showSuccess: (title: string, message: string) => {
      // 这里可以调用父组件的showSuccess，或者使用toast
      console.log(`✅ ${title}: ${message}`);
    },
    showWarning
  });

  // 删除历史记录，防止节点ID重用
  const [deletedNodeHistory, setDeletedNodeHistory] = useState<Set<string>>(new Set());

  const canvasOperationsHook = useCanvasOperations({
    nodes: nodesState,
    edges: edgesState,
    nodesDetailsMap,
    onNodesChange,
    onEdgesChange,
    setNodes: setNodesState,
    setEdges: setEdgesState,
    updateNodeDetails,
    deleteNodeCompletely,
    showError,
    showSuccess: (title: string, message: string) => {
      console.log(`✅ ${title}: ${message}`);
    },
    showWarning,
    deletedNodeHistory
  });

  const connectConfigHook = useConnectConfig({
    showError,
    showWarning,
    showSuccess: (title: string, message: string) => {
      console.log(`✅ ${title}: ${message}`);
    }
  });

  // 设置画布状态变化回调
  useEffect(() => {
    canvasOperationsHook.setCanvasStateChangeCallback((nodes, edges) => {
      onCanvasStateChange?.(nodes, edges);
    });
  }, [canvasOperationsHook, onCanvasStateChange]);

  // 注意：复制粘贴状态已经在 canvasOperationsHook 中管理，这里不需要重复定义



  /**
   * 处理节点变化事件
   * 响应节点的移动、删除等操作，并清理相关数据
   */
  const handleNodesChange = useCallback((changes: any) => {
    onNodesChange(changes);

    // 通知父组件画布状态变化
    setTimeout(() => {
      if (onCanvasStateChange) {
        onCanvasStateChange(nodesState, edgesState);
      }
    }, 0);

    // 处理节点删除事件，使用统一的删除方法
    const deleteChanges = changes.filter((change: any) => change.type === 'remove');
    if (deleteChanges.length > 0) {
      deleteChanges.forEach((change: any) => {
        const nodeId = change.id;

        // 添加到删除历史记录
        setDeletedNodeHistory(prev => {
          const newHistory = new Set(prev);
          newHistory.add(nodeId);
          return newHistory;
        });

        // 5秒后从删除历史中移除，避免永久占用内存
        setTimeout(() => {
          setDeletedNodeHistory(prev => {
            const newHistory = new Set(prev);
            if (newHistory.has(nodeId)) {
              newHistory.delete(nodeId);
              console.log('🧹 [WorkflowEditor] Removed from deletion history after 5s:', nodeId);
            }
            return newHistory;
          });
        }, 5000);

        // 如果当前正在显示被删除节点的详情，关闭详情面板
        if (selectedNodeDetails?.node?.id === nodeId) {
          setSelectedNodeDetails(null);
        }

        // 使用统一的删除方法，完整清理所有相关数据
        deleteNodeCompletely(nodeId);
      });
    }
  }, [onNodesChange, deleteNodeCompletely, selectedNodeDetails, nodesState, edgesState, onCanvasStateChange]);

  // 移除重复的键盘事件处理，现在由WorkflowCanvas组件统一处理

  /**
   * 处理节点双击 - 打开节点配置面板
   */
  const handleNodeDoubleClick = useCallback(async (event: React.MouseEvent, node: Node) => {
    const nodeInstanceId = node.id;
    const cachedDetails = nodesDetailsMap[nodeInstanceId];

    if (!cachedDetails) {
      showError('节点错误', `找不到节点详情: ${nodeInstanceId}`);
      return;
    }

    // 计算前置节点列表
    const previousNodeIds = getAllPreviousNodeIds(nodeInstanceId, edgesState, nodesDetailsMap);

    // 检查是否已有参数数据，如果没有则尝试获取
    let parameters = cachedDetails.parameters || cachedDetails.nodeInfo?.data?.parameters || [];
    
    // 如果参数为 null 或空数组，且有 originalNodeKind，尝试从 API 获取
    if ((!parameters || parameters.length === 0) && cachedDetails.originalNodeKind) {
      try {
        console.log('🔄 [handleNodeDoubleClick] Fetching parameters for node:', nodeInstanceId);
        const response = await fetch(`/api/nodes/${cachedDetails.originalNodeKind}`);
        if (response.ok) {
          const nodeDefData = await response.json();
          parameters = nodeDefData.node?.fields || nodeDefData.node?.parameters || [];
          
          // 更新缓存的节点详情
          const updatedDetails = {
            ...cachedDetails,
            parameters,
            nodeInfo: {
              ...cachedDetails.nodeInfo,
              data: {
                ...cachedDetails.nodeInfo.data,
                parameters
              }
            }
          };
          updateNodeDetails(nodeInstanceId, updatedDetails);
          console.log('✅ [handleNodeDoubleClick] Updated parameters for node:', nodeInstanceId);
        } else {
          console.warn('⚠️ [handleNodeDoubleClick] Failed to fetch node definition');
        }
      } catch (error) {
        console.error('❌ [handleNodeDoubleClick] Error fetching node definition:', error);
      }
    }

    const nodeDetailsData = {
      node: cachedDetails.nodeInfo,
      parameters,
      savedValues: cachedDetails.savedValues || {},
      onTest: (nodeValues: Record<string, any>) => nodeTestingHook.handleNodeTest(nodeValues, nodeInstanceId),
      onTestPreviousNode: (nodeValues: Record<string, any>, targetNodeId: string) => 
        nodeTestingHook.handleLeftPanelNodeTest(nodeValues, targetNodeId),
      onSaveMockData: (mockTestResult: any) => nodeTestingHook.handleSaveMockData(mockTestResult, nodeInstanceId),
      testOutput: nodeTestOutput,
      lastTestResult: cachedDetails.lastTestResult,
      previousNodeIds,
      onPreviousNodeChange: (nodeId: string) => {
        // 处理前置节点切换
      },
      selectedPreviousNodeId: previousNodeIds[0] || '',
      nodesTestResultsMap,
      getLatestNodesTestResultsMap: nodeTestingHook.getLatestNodesTestResultsMap,
      nodesDetailsMap,
      showToast: (type: 'error' | 'warning', title: string, message: string) => {
        if (type === 'error') {
          showError(title, message);
        } else {
          showWarning(title, message);
        }
      }
    };

    setSelectedNodeDetails(nodeDetailsData);
  }, [
    nodesDetailsMap,
    edgesState,
    nodeTestOutput,
    nodesTestResultsMap,
    nodeTestingHook,
    showError,
    showWarning,
    updateNodeDetails
  ]);

  /**
   * 处理节点配置保存
   */
  const handleNodeUpdate = useCallback((nodeData: any) => {
    if (nodeData === null) {
      // 关闭配置面板
      setSelectedNodeDetails(null);
      return;
    }

    // 保存节点配置
    const { nodeId, savedValues, nodeIdChanged, oldNodeId } = nodeData;

    if (nodeIdChanged && oldNodeId) {
      // 处理节点ID变更
      // 这里需要实现节点ID变更逻辑
    }

    // 更新节点详情
    updateNodeDetails(nodeId, { savedValues });

    // 关闭配置面板
    setSelectedNodeDetails(null);
  }, [updateNodeDetails]);

  /**
   * 处理节点ID变更
   */
  const handleNodeIdChange = useCallback((oldId: string, newId: string) => {
    // 这里需要实现完整的节点ID变更逻辑
    // 包括更新nodes、edges、nodesDetailsMap等
  }, []);

  /**
   * 处理菜单折叠状态变化
   */
  const handleMenuCollapseChange = useCallback((collapsed: boolean) => {
    setMenuCollapsed(collapsed);
    onMenuCollapseChange(collapsed);
  }, [onMenuCollapseChange]);

  // 使用connect config hook的函数，如果父组件没有提供的话
  const finalFetchConnectConfigs = onFetchConnectConfigs || connectConfigHook.handleFetchConnectConfigs;
  const finalFetchTables = onFetchTables || connectConfigHook.handleFetchTables;

  return (
    <ErrorBoundary>
      <WorkflowEditorContainer>
        {/* 左侧节点菜单 */}
        <NodeMenu
          onMenuCollapseChange={handleMenuCollapseChange}
          showError={showError}
          showWarning={showWarning}
        />

        {/* 右侧工作流画布 */}
        <CanvasContainer>
          <WorkflowCanvas
            nodes={nodesState}
            edges={edgesState}
            onNodesChange={handleNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={canvasOperationsHook.handleConnect}
            onDrop={canvasOperationsHook.handleDrop}
            onDragOver={(event) => {
              event.preventDefault();
              event.dataTransfer.dropEffect = 'move';
            }}
            // onSelectionChange={(elements) => {
            //   console.log('选择变化:', elements);
            // }}
            onNodeUpdate={handleNodeUpdate}
            onNodeDoubleClick={handleNodeDoubleClick}
            selectedNodeDetails={selectedNodeDetails}
            onNodeIdChange={handleNodeIdChange}
            nodeWidth={selectedNodeDetails?.node?.data?.nodeWidth}
            onAutoLayout={(layoutedNodes) => {
              setNodesState(layoutedNodes);
            }}
            onCopyNodes={canvasOperationsHook.handleCopyNodes}
            onPasteNodes={async () => {
              // 首先检查是否有内部复制的节点
              if (canvasOperationsHook.copyPasteState.copiedNodes.length > 0) {
                canvasOperationsHook.handlePasteNodes();
                return;
              }

              // 如果没有内部复制的节点，尝试从系统剪贴板导入
              try {
                const clipboardText = await navigator.clipboard.readText();
                if (clipboardText.trim()) {
                  try {
                    const clipboardData = JSON.parse(clipboardText);
                    if (clipboardData && clipboardData.nodes && clipboardData.edges) {
                       console.log('📋 [画布粘贴] 从系统剪贴板导入工作流数据');
                       await canvasOperationsHook.handleImportFromClipboard(clipboardText);
                       return;
                     }
                  } catch (parseError) {
                    // 不是JSON格式，忽略
                  }
                }
              } catch (error) {
                console.error('❌ [画布粘贴] 读取剪贴板失败:', error);
              }

              // 如果都失败了，尝试普通粘贴
              canvasOperationsHook.handlePasteNodes();
            }}
            nodesTestResultsMap={nodesTestResultsMap}
            getLatestNodesTestResultsMap={nodeTestingHook.getLatestNodesTestResultsMap}
            connectConfigs={[]} // 这里可以传入缓存的连接配置
            onFetchConnectConfigs={finalFetchConnectConfigs}
            onFetchTables={finalFetchTables}
          />
        </CanvasContainer>
      </WorkflowEditorContainer>
    </ErrorBoundary>
  );
};

export default WorkflowEditor;