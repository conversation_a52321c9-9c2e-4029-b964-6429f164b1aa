"use client";
import React, { memo, useMemo } from 'react';
// @ts-ignore
import { Handle, Position, NodeProps } from 'reactflow';
import styled from 'styled-components';

const NodeContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
 // pointer-events: none; /* 禁用整个容器的拖拽 */
`;

const DraggableArea = styled.div`
  pointer-events: auto; /* 只有这个区域可以拖拽 */
`;

const NodeGraph = styled.div<{ $nodeType: string }>`
  position: relative;
  padding: 10px 15px;
  border-radius: 5px;
  background: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.border};
  width: 90px;
  height: 80px;
  min-width: 68px;
  max-width: 98px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: ${({ theme }) => theme.colors.accent};
  }
`;

const TriggerNodeGraph = styled.div<{ $nodeType: string }>`
  position: relative;
  padding: 10px 15px;
  border-radius: 25px 5px 5px 25px;
  background: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.border};
  width: 90px;
  height: 80px;
  min-width: 68px;
  max-width: 98px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: ${({ theme }) => theme.colors.accent};
  }
`;

const NodeHeader = styled.div`
  margin: 3px 0px 3px 0px;
`;

const NodeIcon = styled.div<{ $bgColor: string }>`
  width: 48px;
  height: 50px;
  border-radius: 4px;
  //background-color: ${props => props.$bgColor};
  display: flex;
  align-items: center;
  justify-content: center;
  //margin-right: 8px;
  color: white;
`;

//这画布上的节点信息，在节点上显示
const NodeTitle = styled.div`
  font-weight: 500;
  font-size: 12px;
  margin-top: 6px;
  color: ${({ theme }) => theme.colors.textSecondary};
  width: 80px;
  max-width: 90px;
  min-width: 68px;
  text-align: center;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  flex-shrink: 0;
`;

// const NodeDescription = styled.div`
//   font-size: 10px;
//   color: ${({ theme }) => theme.colors.textSecondary};
//   margin-left: 0px;
// `;

// 定义拖在画布上的节点的样式
const CustomHandle = styled(Handle)`
  width: 20px;
  height: 20px;
  background: ${({ theme }) => theme.colors.accent}80;
  border: 1px solid ${({ theme }) => theme.colors.primary};
  position: absolute;
  
   /* 向外扩展连线点位置 */
  &.left {
    left: -7px; /* 向左扩展 */
    border-radius: 1px;
    width: 14px;
    height: 22px;
    top: 50%;
    transform: translateY(-50%);
   }
  &.right {
    right: -10px; /* 向右扩展 */
    top: 50%;
    transform: translateY(-50%);
  }
  &:hover {
    background: ${({ theme }) => theme.colors.accent};
  }
`;

const NodeLinkInfo = styled.div`
  margin-top: 8px;
  padding: 6px 8px;
  background-color: ${({ theme }) => theme.colors.tertiary};
  border-radius: 4px;
  font-size: 10px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const LinkSection = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const LinkLabel = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const LinkValue = styled.span`
  color: ${({ theme }) => theme.colors.accent};
`;

// Custom node for actions
export const ActionNodeComponent = memo(({ data }: NodeProps) => {
  // 使用 useMemo 优化连接点渲染
  const handles = useMemo(() => {
    const handles: React.ReactElement[] = [];
    
    // 渲染输入连接点
    if (data.link?.inputs && data.link.inputs.length > 0) {
      data.link.inputs.forEach((input: any, index: number) => {
        const handleTop = data.link.inputs.length === 1 
          ? 50 // 单个连接点居中
          : (100 / (data.link.inputs.length + 1)) * (index + 1); // 多个连接点均匀分布
        
        // 为每个连接点生成独立的提示
        const inputTitle = input.desc && input.desc.trim() !== '' ? input.desc : '';//输入
        
        handles.push(
          <CustomHandle 
            key={`input-${index}`}
            type="target" 
            position={Position.Left} 
            id={`left-${index}`} 
            className="left"
            title={inputTitle}
            style={{ top: `${handleTop}%`, transform: 'translateY(-50%)' }}
          />
        );
      });
    }
    
    // 渲染输出连接点
    if (data.link?.outputs && data.link.outputs.length > 0) {
      data.link.outputs.forEach((output: any, index: number) => {
        const handleTop = data.link.outputs.length === 1 
          ? 50 // 单个连接点居中
          : (100 / (data.link.outputs.length + 1)) * (index + 1); // 多个连接点均匀分布
        
        // 为每个连接点生成独立的提示
        const outputTitle = output.desc && output.desc.trim() !== '' ? output.desc : '';//输出
        
        handles.push(
          <CustomHandle 
            key={`output-${index}`}
            type="source" 
            position={Position.Right} 
            id={`right-${index}`} 
            className="right"
            title={outputTitle}
            style={{ top: `${handleTop}%`, transform: 'translateY(-50%)' }}
          />
        );
      });
    }
    
    return handles;
  }, [data.link]); // 添加依赖项

  return (
    <NodeContainer>
      <DraggableArea>
        <NodeGraph $nodeType="action">
          {handles}
          <NodeHeader>
            <NodeIcon $bgColor="#33C2EE">
              <img src={data.icon} alt={data.title} style={{  width: '36px', height: '36px' }} />
            </NodeIcon>
          </NodeHeader>
        </NodeGraph>
      </DraggableArea>
      <NodeTitle>{data.name}</NodeTitle>
    </NodeContainer>
  );
});

ActionNodeComponent.displayName = 'ActionNodeComponent';

// Custom node for actions
export const TriggerNodeComponent = memo(({ data }: NodeProps) => {
   // 使用 useMemo 优化连接点渲染
   const handles = useMemo(() => {
     const handles: React.ReactElement[] = [];
     
     // 渲染输入连接点
     if (data.link?.inputs && data.link.inputs.length > 0) {
       data.link.inputs.forEach((input: any, index: number) => {
         const handleTop = data.link.inputs.length === 1 
           ? 50 // 单个连接点居中
           : (100 / (data.link.inputs.length + 1)) * (index + 1); // 多个连接点均匀分布
         
         // 为每个连接点生成独立的提示
         const inputTitle = input.desc && input.desc.trim() !== '' ? input.desc : '输入';
         
         handles.push(
           <CustomHandle 
             key={`input-${index}`}
             type="target" 
             position={Position.Left} 
             id={`left-${index}`} 
             className="left"
             title={inputTitle}
             style={{ top: `${handleTop}%`, transform: 'translateY(-50%)' }}
           />
         );
       });
     }
     
     // 渲染输出连接点
     if (data.link?.outputs && data.link.outputs.length > 0) {
       data.link.outputs.forEach((output: any, index: number) => {
         const handleTop = data.link.outputs.length === 1 
           ? 50 // 单个连接点居中
           : (100 / (data.link.outputs.length + 1)) * (index + 1); // 多个连接点均匀分布
         
         // 为每个连接点生成独立的提示
         const outputTitle = output.desc && output.desc.trim() !== '' ? output.desc : '输出';
         
         handles.push(
           <CustomHandle 
             key={`output-${index}`}
             type="source" 
             position={Position.Right} 
             id={`right-${index}`} 
             className="right"
             title={outputTitle}
             style={{ top: `${handleTop}%`, transform: 'translateY(-50%)' }}
           />
         );
       });
     }
     
     return handles;
   }, [data.link]); // 添加依赖项

   return (
    <NodeContainer>
      <DraggableArea>
        <TriggerNodeGraph $nodeType="trigger">
          {handles}
          <NodeHeader>
            <NodeIcon $bgColor="#33C2EE">
              <img src={data.icon} alt={data.title} style={{  width: '36px', height: '36px' }} />
            </NodeIcon>
          </NodeHeader>
        </TriggerNodeGraph>
      </DraggableArea>
      <NodeTitle>{data.name}</NodeTitle>
    </NodeContainer>
  );
});

TriggerNodeComponent.displayName = 'TriggerNodeComponent';



// Custom node for conditions
// export const ConditionNodeComponent = memo(({ data }: NodeProps) => {
//   return (
//     <NodeContainer $nodeType="condition">
//       <Handle type="target" position={Position.Top} />
//       <Handle type="source" position={Position.Bottom} id="a" />
//       <Handle type="source" position={Position.Right// d="b" />
//       {/* 添加左侧连接点 */}
//       <Handle type="target" posi// n={Position.// t} id="left-target" />
//       <Handle ty// "source" position={Position.Left} id="left-source" />
//       <NodeHeader>
//         <NodeIcon $bgColor="#FF9800">
//  //       <img src={data.icon} alt={data.title} style={{ width: '16p // height: '16px' }} />
//        </NodeIcon>
//         <NodeTitle>{data.title}</NodeTitle>
//    // </NodeHeader>
//       <NodeDescription>{data.description}</NodeDescriptio//
//     </NodeContai// >
//   );
// });

// ConditionNodeC// onent.displayName = 'ConditionNodeComponent';// // // // // // // //