"use client";

import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import styled from 'styled-components';
import { ParameterInput } from './ParameterInput';
import { LeftPanel } from './LeftPanel';
import { RightPanel } from './RightPanel';
import { SiSpeedtest } from "react-icons/si";
import { FaSave } from "react-icons/fa";
import { MdCancel } from "react-icons/md";
import { BiSolidShow } from "react-icons/bi";
import { TbEyeClosed } from "react-icons/tb";
import { FiEdit2 } from "react-icons/fi";
import { CoButton } from '../components/basic/Buttons';
import type {
  NodeSettingsProps,
  TestResult,
  ValueChangeHandler,
  NodeIdChangeHandler,
  MouseEventHandler,
  KeyboardEventHandler,
  ChangeEventHandler
} from './types';



// Main container taking up 98vh x 98vh
const MainContainer = styled.div`
  position: fixed;
  top: 1vh;
  left: 1vw;
  width: 98vw;
  height: 97vh;
  overflow: hidden;
  display: flex;
  user-select: none;
  z-index: 1000;
`;

// 中心轴Draggable divider
const Divider = styled.div<{ onMouseDown: (e: React.MouseEvent) => void }>`
  width: 4px;
  height: 99%;
  /*background: ${({ theme }) => theme.colors.border};*/
  background: #1e3a5f;
  cursor: col-resize;
  position: relative;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors.accent};
  }
  
  &::before {
    content: '';
    position: absolute;
    left: -6px;
    right: -6px;
    top: 0;
    bottom: 0;
    cursor: col-resize;
  }
`;

// Floating settings overlay
const SettingsOverlay = styled.div.attrs<{ $visible: boolean; $leftOffset: number; $nodeWidth: number; $expanded?: boolean }>(({ $visible, $leftOffset, $nodeWidth, $expanded }) => ({
  style: {
    left: `${$leftOffset}%`,
    width: $expanded ? '100%' : `${$nodeWidth}px`,
    display: $visible ? 'block' : 'none',
  },
})) <{ $visible: boolean; $leftOffset: number; $nodeWidth: number; $expanded?: boolean }>`
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  height: 96vh;
  background:  ${({ theme }) => theme.panel.nodeBg};
  border-radius: 4px;
  border:1px solid #ffffff25;
  box-shadow: 0px 0px 8px rgba(64, 93, 158, 0.4);
  z-index: 10001;
  overflow: hidden;
  transition: left 0.1s ease;
`;

const SettingsContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px 25px 20px 25px;
`;

const FixedHeader = styled.div`
  flex-shrink: 0;
`;

const ScrollableContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
  margin-right: -10px;
`;

const FixedFooter = styled.div`
  flex-shrink: 0;
  margin-top: 25px;
`;

const TitleHeader = styled.div<{ onMouseDown: (e: React.MouseEvent) => void }>`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.accent};
  cursor: pointer;
  text-align: center;
  justify-content: center;
  margin-bottom: 12px;
`;

const TitleDesc = styled.div`
  display:flex;
  align-items: center;
  justify-content: center;
`;

const TitleIcon = styled.img`
  width: 24px;
  height: 24px;
  margin-top:-8px;
  margin-right: 6px;
  color:#2d3849;
`;

// 可编辑标签容器
const EditableTitleContainer = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
`;

// 标签样式
const TitleLabel = styled.label`
  font-size: 14px;
  /*color: ${({ theme }) => theme.colors.textPrimary};*/
  background: ${({ theme }) => theme.panel.nodeBg};
  //border: 1px solid #1e3a5f;
  padding: 6px 6px;
  font-weight: 600;
  cursor: pointer;
  flex: 1;
  //transition: all 0.2s ease;
  
  &:hover {
    // border: 1px solid #1e3a5f;
    // border-bottom: 1px solid ${({ theme }) => theme.colors.borderHover};
    // background: ${({ theme }) => theme.panel.nodeBg};
  }
`;

// 编辑图标
const EditIcon = styled(FiEdit2)`
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-left: 8px;
  cursor: pointer;
  transition: color 0.2s ease;
  
  &:hover {
    color: ${({ theme }) => theme.colors.accent};
  }
`;

// 编辑模式下的输入框
const EditInput = styled.input`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textPrimary};
  background: ${({ theme }) => theme.panel.nodeBg};
  border: 0px;
  border-bottom: 2px solid ${({ theme }) => theme.colors.accent};
  border-radius: 2px;
  padding: 8px 6px;
  font-weight: 600;
  flex: 1;
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.accent};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.accent}20;
  }
  
`;

const Tabs = styled.div`
  display: flex;
  margin-bottom: 14px;
  border-bottom: 1px solid ${({ theme }) => theme.panel.ctlBorder};
  font-size:14px;
`;

const Tab = styled.div<{ $active?: boolean }>`
  padding: 8px 12px;
  cursor: pointer;
  color: ${props => props.$active ? props.theme.colors.accent : props.theme.colors.textSecondary};
  border-bottom: 2px solid ${props => props.$active ? props.theme.colors.accent : 'transparent'};
  transition: color 0.2s ease;
  font-weight: ${props => props.$active ? '400' : '200'};
  
  &:hover {
    color: ${({ theme }) => theme.colors.accent};
  }
`;

const ButtonContainer = styled.div`
  display: flex; 
  margin-top: -10px;
  justify-content: space-between;
`;

//节点测试
const TestButton = styled.button<{ $disabled?: boolean }>`
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  padding: 0px 14px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
   cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};
  height:28px;
  backdrop-filter: blur(4px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)';
  &:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
    //transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  &:after {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
    //transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  &:active {
    transform: ${({ $disabled }) => $disabled ? 'none' : 'translateY(0)'};
  }
  
  .loading-icon {
    animation: spin 1s linear infinite;
    font-size: 14px;
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  svg {
    margin-bottom: -1px;
    margin-right: 8px; 
  }
`;

const CommonBtnContainer = styled.div`
  display: flex;
  gap: 15px;
`;


// Toggle button to show/hide settings
const ToggleSettingsButton = styled.button`
  position: absolute;
  top: 8px;
  right: 100px;
  background: ${({ theme }) => theme.panel.panelBg};
  border: 1px solid rgba(255, 255, 255, 0.35);
  border-radius: 4px;
  padding: 0px 11px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  height:28px;
  backdrop-filter: blur(4px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  &:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 4px 12px rgba(2, 2, 2, 0.15);
  }

  svg{
      margin-right: 0px;
      width:18px;
      height:18px;
  }
`;

// Close button for the entire panel
const CloseButton = styled.button`
  position: absolute;
  top: 8px;
  right: 20px;
  background: rgba(243, 8, 8, 0.58);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  padding: 0px 14px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  height:28px;
  backdrop-filter: blur(4px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)';
  &:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 4px 12px rgba(2, 2, 2, 0.15);
  }
`;

// Output content containers
const OutputContainer = styled.div`
  background: ${({ theme }) => theme.colors.inputBg};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: ${({ theme }) => theme.colors.textPrimary};
  min-height: 500px;
  height: 93%;
  white-space: pre-wrap;
  word-break: break-word;
`;

// NodeSettingsProps is now imported from McpInterfaces.ts.ts

export const NodeSettings: React.FC<NodeSettingsProps> = ({
  node,
  parameters,
  savedValues = {},
  onClose,
  onSave,
  onNodeIdChange,
  previousNodeOutput = '',
  onTest,
  onTestPreviousNode,
  onSaveMockData,
  testOutput = '',
  nodeWidth,
  lastTestResult,
  previousNodeIds,
  onPreviousNodeChange,
  selectedPreviousNodeId,
  nodesTestResultsMap,
  getLatestNodesTestResultsMap,
  nodesDetailsMap,
  showToast,
  connectConfigs = [],
  onFetchConnectConfigs,
  onFetchTables,
}) => {
  const [activeTab, setActiveTab] = useState('parameters');
  const [nodeId, setNodeId] = useState(node.id);
  const [nodeIcon, setnodeIcon] = useState(node.data.icon);
  const [showSettings, setShowSettings] = useState(true);
  const [leftWidth, setLeftWidth] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const [overlayLeftOffset, setOverlayLeftOffset] = useState(50);
  const [isExpanded, setIsExpanded] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [displayedTestResult, setDisplayedTestResult] = useState<any>(null);
  // 添加测试状态管理
  const [isTestting, setIsTestting] = useState(false);
  
  // 动态连接配置状态管理
  const [dynamicConnectConfigs, setDynamicConnectConfigs] = useState<Record<string, Array<{
    id: string;
    name: string;
    ctype: string;
    nodeinfo: Record<string, any>;
    description?: string;
  }>>>({});

  // 动态获取连接配置的函数
  const fetchConnectConfigsByType = useCallback(async (ctype: string) => {
    // 如果已经缓存了该类型的配置，直接返回
    if (dynamicConnectConfigs[ctype]) {
      return dynamicConnectConfigs[ctype];
    }

    // 如果有回调函数，调用web层获取数据
    if (onFetchConnectConfigs) {
      try {
        const configs = await onFetchConnectConfigs(ctype);
        // 缓存结果
        setDynamicConnectConfigs(prev => ({
          ...prev,
          [ctype]: configs
        }));
        return configs;
      } catch (error) {
        console.error('获取连接配置失败:', error);
        return [];
      }
    }

    // 如果没有回调函数，从静态配置中过滤
    const filteredConfigs = connectConfigs.filter(config => config.ctype === ctype);
    return filteredConfigs;
  }, [dynamicConnectConfigs, onFetchConnectConfigs, connectConfigs]);

  // 添加编辑状态管理
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [tempNodeId, setTempNodeId] = useState(nodeId);
  const editInputRef = useRef<HTMLInputElement>(null);

  // 监听testOutput变化，重置加载状态（仅当testOutput确实是新的测试结果时）
  useEffect(() => {
    if (testOutput && isTestting) {
      setIsTestting(false);
      console.log('✅ [NodeSettings] Test output received, loading state reset');
    }
  }, [testOutput, isTestting]);

  // 创建一个ref来跟踪上次的lastTestResult，避免模拟数据触发状态重置
  const lastTestResultRef = useRef(lastTestResult);
  useEffect(() => {
    // 只有当lastTestResult真正发生变化且不是设置模拟数据时才重置状态
    if (lastTestResult && lastTestResult !== lastTestResultRef.current && isTestting) {
      setIsTestting(false);
      console.log('✅ [NodeSettings] New test result received, loading state reset');
    }
    lastTestResultRef.current = lastTestResult;
  }, [lastTestResult, isTestting]);

  // 优化: 使用useMemo初始化nodeValues，避免每次渲染重新计算
  const initialNodeValues = useMemo(() => {
    const initialValues: Record<string, any> = {};

    parameters.forEach(param => {
      let value;

      // 优先级：savedValues > node.data > param.default
      if (savedValues[param.name] !== undefined) {
        value = savedValues[param.name];
      } else if (node.data?.[param.name] !== undefined) {
        value = node.data[param.name];
      } else {
        value = param.default;
      }

      initialValues[param.name] = value;
    });

    return initialValues;
  }, [parameters, savedValues, node.data]);

  const [nodeValues, setNodeValues] = useState<Record<string, any>>(initialNodeValues);
  const [hasUserInput, setHasUserInput] = useState<boolean>(false);

  // 优化: 当initialNodeValues变化时更新nodeValues，但只在用户还没有输入时才更新
  useEffect(() => {
    if (!hasUserInput) {
      setNodeValues(initialNodeValues);
    }
  }, [initialNodeValues, hasUserInput]);

  // 组件初始化时打印nodesDetailsMap
  useEffect(() => {
    console.log('NodeSettings界面打开 - nodesDetailsMap:', nodesDetailsMap);
    console.log('NodeSettings - activeTab:', activeTab);
    console.log('NodeSettings - showSettings:', showSettings);
    console.log('NodeSettings - parameters:', parameters);
    console.log('NodeSettings - parameters.length:', parameters.length);
  }, []);



  const actualNodeWidth = useMemo(() => {
    // 确保节点配置面板有合理的最小宽度，不受自动布局影响
    const minWidth = 600; // 设置最小宽度为600px
    const maxWidth = 1000; // 设置最大宽度为1000px
    const defaultWidth = 800; // 默认宽度

    let width = nodeWidth || (node?.data as any)?.nodeWidth || defaultWidth;

    // 如果宽度小于最小值，使用默认宽度
    if (width < minWidth) {
      width = defaultWidth;
    }

    // 如果宽度大于最大值，限制为最大宽度
    if (width > maxWidth) {
      width = maxWidth;
    }

    return width;
  }, [nodeWidth, node?.data]);



  // 初始化和窗口大小改变时重新计算浮窗位置
  useEffect(() => {
    const updateOverlayPosition = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();

        // 内联计算浮窗位置，避免依赖外部函数
        const overlayWidth = actualNodeWidth;
        const overlayHalfWidth = overlayWidth / 2;
        const leftPanelCenter = (leftWidth / 100) * rect.width;
        const rightEdge = leftPanelCenter + overlayHalfWidth;
        const leftEdge = leftPanelCenter - overlayHalfWidth;

        let finalPosition = leftWidth; // 默认跟随左侧面板中心

        // 如果超出右边界，调整位置
        if (rightEdge > rect.width) {
          finalPosition = ((rect.width - overlayHalfWidth) / rect.width) * 100;
        }

        if (leftEdge < 0) {
          finalPosition = (overlayHalfWidth / rect.width) * 100;
        }

        setOverlayLeftOffset(finalPosition);
      }
    };

    updateOverlayPosition();
    window.addEventListener('resize', updateOverlayPosition);

    return () => {
      window.removeEventListener('resize', updateOverlayPosition);
    };
  }, [leftWidth, actualNodeWidth]);

  const handleValueChange = useCallback((name: string, value: any) => {
    setHasUserInput(true);
    setNodeValues((prev: Record<string, any>) => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handleNodeIdChange = useCallback((newId: string) => {
    setNodeId(newId);
  }, []);

  // 处理标题编辑 - 优化回调函数
  const handleTitleClick = useCallback(() => {
    setIsEditingTitle(true);
    setTempNodeId(nodeId);
    setTimeout(() => {
      editInputRef.current?.focus();
      editInputRef.current?.select();
    }, 0);
  }, [nodeId]);

  const handleTitleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setTempNodeId(e.target.value);
  }, []);

  const handleTitleInputBlur = useCallback(() => {
    // 检查新名称是否为空或只有空白字符
    if (!tempNodeId.trim()) {
      showToast?.('warning', '名称无效', '节点名称不能为空');
      setTempNodeId(nodeId); // 恢复原名称
      setIsEditingTitle(false);
      return;
    }

    // 如果名称没有变化，直接关闭编辑模式
    if (tempNodeId === nodeId) {
      setIsEditingTitle(false);
      return;
    }

    // 检查名称是否在nodesDetailsMap中已存在（排除当前节点）
    const isNameExists = Object.keys(nodesDetailsMap || {}).some(existingNodeId => 
      existingNodeId === tempNodeId && existingNodeId !== nodeId
    );

    if (isNameExists) {
      showToast?.('error', '名称冲突', '该节点名称已存在');
      setTempNodeId(nodeId); // 恢复原名称
      setIsEditingTitle(false);
      return;
    }

    // 如果名称有效且不重复，执行更新
    setNodeId(tempNodeId);
    setIsEditingTitle(false);

    // 通知父组件节点ID发生变化
    if (onNodeIdChange) {
      onNodeIdChange(nodeId, tempNodeId);
    }
  }, [tempNodeId, nodeId, nodesDetailsMap, showToast, onNodeIdChange]);

  const handleTitleInputKeyPress = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleTitleInputBlur();
    } else if (e.key === 'Escape') {
      setIsEditingTitle(false);
      setTempNodeId(nodeId);
    }
  }, [handleTitleInputBlur, nodeId]);

  // 优化: 合并保存和取消的逻辑
  const handleCancel = useCallback(() => {
    setHasUserInput(false);
    onClose();
  }, [onClose]);

  const handleSave = useCallback(() => {
    // 数据类型转换逻辑
    const convertedValues: Record<string, any> = {};

    parameters.forEach(param => {
      const value = nodeValues[param.name];
      if (value !== undefined) {
        switch (param.type) {
          case 'number':
            convertedValues[param.name] = typeof value === 'string' ? parseFloat(value) : value;
            break;
          case 'boolean':
            convertedValues[param.name] = typeof value === 'string' ? value === 'true' : Boolean(value);
            break;
          default:
            convertedValues[param.name] = value;
            break;
        }
      }
    });

    onSave({
      ...node,
      id: nodeId,
      data: {
        ...node.data,
        name: nodeId,
        ...convertedValues
      }
    });

    setHasUserInput(false);
  }, [nodeValues, parameters, node, nodeId, onSave]);

  // 优化: 使用useCallback缓存鼠标事件处理函数
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    let percentage = Math.max(10, Math.min(90, (x / rect.width) * 100));

    // 如果浮窗会撞到边界，限制拖拽
    const overlayWidth = actualNodeWidth;
    const overlayHalfWidth = overlayWidth / 2;
    const proposedCenter = (percentage / 100) * rect.width;

    // 检查右边界限制
    if (proposedCenter + overlayHalfWidth > rect.width) {
      percentage = Math.min(percentage, ((rect.width - overlayHalfWidth) / rect.width) * 100);
    }

    // 检查左边界限制
    if (proposedCenter - overlayHalfWidth < 0) {
      percentage = Math.max(percentage, (overlayHalfWidth / rect.width) * 100);
    }

    // 内联计算浮窗位置
    const leftPanelCenter = (percentage / 100) * rect.width;
    const rightEdge = leftPanelCenter + overlayHalfWidth;
    const leftEdge = leftPanelCenter - overlayHalfWidth;

    let overlayPosition = percentage; // 默认跟随左侧面板中心

    // 如果超出右边界，调整位置
    if (rightEdge > rect.width) {
      overlayPosition = ((rect.width - overlayHalfWidth) / rect.width) * 100;
    }

    if (leftEdge < 0) {
      overlayPosition = (overlayHalfWidth / rect.width) * 100;
    }

    setLeftWidth(percentage);
    setOverlayLeftOffset(overlayPosition);
  }, [isDragging, actualNodeWidth]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 提供给子组件的扩展模式控制函数
  const handleExpandModeChange = useCallback((expanded: boolean) => {
    setIsExpanded(expanded);
  }, []);

  // 优化: 使用useCallback缓存LeftPanel回调
  const handleLeftPanelTest = useCallback((nodeValues: Record<string, any>, targetNodeId: string) => {
    onTestPreviousNode?.(nodeValues, targetNodeId);
  }, [onTestPreviousNode]);

  const handleDisplayTestResult = useCallback((testResult: any) => {
    if (testResult) {
      setDisplayedTestResult(testResult);
    } else {
      setDisplayedTestResult(null);
    }
  }, []);

  // 优化: 使用useCallback缓存测试函数
  const handleNodeTest = useCallback(() => {
    // 🔧 确保测试前重置状态，防止模拟数据设置导致的状态异常
    if (isTestting) {
      console.log('⚠️ [NodeSettings] Test already in progress, resetting state first');
      setIsTestting(false);
      // 使用 setTimeout 确保状态更新完成后再开始新的测试
      setTimeout(() => {
        handleNodeTest();
      }, 10);
      return;
    }

    // 设置测试状态为true
    setIsTestting(true);

    if (onTest) {
      console.log('🚀 [NodeSettings] Calling onTest with nodeValues:', nodeValues);

      try {
        onTest(nodeValues);
        console.log('✅ [NodeSettings] Test initiated successfully');
      } catch (error) {
        console.error('❌ [NodeSettings] Test failed:', error);
        setIsTestting(false);
      }
    } else {
      console.error('❌ [TestButton] onTest 函数未定义!');
      setIsTestting(false);
    }
  }, [onTest, nodeValues, node, parameters, isTestting]);

  return (
    <MainContainer ref={containerRef}>
      <ToggleSettingsButton onClick={() => setShowSettings(!showSettings)}>
        {showSettings ? <BiSolidShow /> : <TbEyeClosed />}
      </ToggleSettingsButton>
      <CloseButton onClick={onClose}>关闭 ✖</CloseButton>
      <LeftPanel
        width={leftWidth}
        previousNodeOutput={previousNodeOutput}
        nodeId={nodeId}
        previousNodeIds={previousNodeIds}
        onPreviousNodeChange={onPreviousNodeChange}
        selectedPreviousNodeId={selectedPreviousNodeId}
        nodesTestResultsMap={nodesTestResultsMap}
        getLatestNodesTestResultsMap={getLatestNodesTestResultsMap}
        nodesDetailsMap={nodesDetailsMap}
        showToast={showToast}
        nodeWidth={actualNodeWidth}
        showSettings={showSettings}
        onTest={handleLeftPanelTest}
        onDisplayTestResult={handleDisplayTestResult}
      />

      <Divider onMouseDown={handleMouseDown} />

      <RightPanel
        width={100 - leftWidth}
        onTest={onTest}
        onSaveMockData={onSaveMockData}
        testOutput={testOutput}
        nodeWidth={actualNodeWidth}
        showSettings={showSettings}
        lastTestResult={lastTestResult}
        nodeId={nodeId}
        nodesTestResultsMap={nodesTestResultsMap}
        onUpdateNodesTestResultsMap={(id: string, data: any) => {
          // 通过现有的onSaveMockData回调来更新数据
          console.log('✅ [NodeSettings] 通过onSaveMockData更新数据:', { nodeId: id, data });



          // 🔧 设置模拟数据时确保不影响测试状态
          const currentTestingState = isTestting;
          console.log('🔧 [NodeSettings] Current testing state before mock data:', currentTestingState);

          // 创建符合mockTestResult格式的数据
          const mockTestResult = {
            timestamp: new Date().toISOString(),
            success: true,
            runData: data,
            inputs: {},
            source: 'mock',
            nodeKind: 'unknown'
          };

          if (onSaveMockData) {
            onSaveMockData(mockTestResult);
          }

          // 🔧 如果之前不是在测试状态，确保设置模拟数据后也不是测试状态
          setTimeout(() => {
            if (!currentTestingState && isTestting) {
              console.log('🔧 [NodeSettings] Resetting test state after mock data setting');
              setIsTestting(false);
            }
          }, 10);
        }}
      />

      <SettingsOverlay
        $visible={showSettings}
        $leftOffset={overlayLeftOffset}
        $nodeWidth={actualNodeWidth}
        $expanded={isExpanded}
      >
        <SettingsContainer>
          <TitleHeader onMouseDown={handleMouseDown}>:::::::::</TitleHeader>
          <FixedHeader>
            <TitleDesc>
              <TitleIcon src={nodeIcon} alt={nodeId} />
              <EditableTitleContainer>
                {isEditingTitle ? (
                  <EditInput
                    ref={editInputRef}
                    value={tempNodeId}
                    onChange={handleTitleInputChange}
                    onBlur={handleTitleInputBlur}
                    onKeyDown={handleTitleInputKeyPress}
                    placeholder="Node ID"
                    data-no-json-drop="true"
                  />
                ) : (
                  <>
                    <TitleLabel onClick={handleTitleClick}>{nodeId}</TitleLabel>
                    {/*  <TitleLabel onClick={handleTitleClick}>{nodeId}</TitleLabel>
                   <EditIcon onClick={handleTitleClick} /> */}
                  </>
                )}
              </EditableTitleContainer>
            </TitleDesc>
            <Tabs>
              <Tab
                $active={activeTab === 'parameters'}
                onClick={() => setActiveTab('parameters')}
              >
                参数
              </Tab>
              <Tab
                $active={activeTab === 'settings'}
                onClick={() => setActiveTab('settings')}
              >
                配置
              </Tab>
            </Tabs>
          </FixedHeader>

          <ScrollableContent>
            {activeTab === 'parameters' && (
              <div>
                {parameters.map(param => {
                  return (
                    <ParameterInput
                      key={param.name}
                      parameter={param}
                      value={nodeValues[param.name]}
                      onChange={handleValueChange}
                      formValues={nodeValues}
                      onExpandModeChange={handleExpandModeChange}
                      connectConfigs={connectConfigs}
                      onFetchConnectConfigs={fetchConnectConfigsByType}
                      onFetchTables={onFetchTables}
                    />
                  );
                })}
              </div>
            )}
          </ScrollableContent>

          <FixedFooter>
            <ButtonContainer>
              <TestButton
                $disabled={false}
                onClick={() => {
                  // 🔧 强制重置测试状态，确保按钮始终可点击
                  if (isTestting) {
                    console.log('🔧 [NodeSettings] Force resetting test state on button click');
                    setIsTestting(false);
                    setTimeout(() => {
                      handleNodeTest();
                    }, 10);
                  } else {
                    handleNodeTest();
                  }
                }}
              >
                {isTestting ? (
                  <>
                    <span className="loading-icon">⏳</span>
                    测试中...
                  </>
                ) : (
                  <>
                    <SiSpeedtest />
                    节点测试
                  </>
                )}
              </TestButton>
              <CommonBtnContainer>
                <CoButton variant='Glass'  onClick={handleCancel}><MdCancel />取消</CoButton>
                <CoButton variant='Glass' backgroundColor='linear-gradient(135deg, #2c6feb, #1a4fb3)' onClick={handleSave}><FaSave />保存</CoButton> 
                {/* <Button $primary onClick={handleSave}><FaSave />保存</Button> */}
              </CommonBtnContainer>
            </ButtonContainer>
          </FixedFooter>
        </SettingsContainer>
      </SettingsOverlay>
    </MainContainer>
  );
};