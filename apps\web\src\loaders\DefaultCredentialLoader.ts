import { CredentialListOptions, ICredentialLoader, CredentialData } from "@repo/common";
import { prisma } from "@repo/db";

export class DefaultCredentialLoader implements ICredentialLoader {
    async get(id: string): Promise<CredentialData | null | undefined> {
        const connectConfig = await prisma.connectConfig.findUnique({
            where: { id: id }
        });

        if (!connectConfig) {
            throw new Error(`连接配置不存在: ${id}`);
        }

        // 解析配置信息
        const configData = JSON.parse(connectConfig.configinfo);

        return {
            id: id,
            config: configData
        } as CredentialData;
    }

    async list(opts?: CredentialListOptions): Promise<CredentialData[] | null | undefined> {
        return [];
    }

}