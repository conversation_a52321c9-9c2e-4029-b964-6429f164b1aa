import { Container, interfaces } from "inversify";

export class BaseContainer<T> {
    readonly #container: Container;
    readonly #identity: string;

    constructor(identity: string) {
        this.#container = new Container();
        this.#identity = identity;
    }

    bind(contactor: interfaces.Newable<T>) {
        this.#container.bind<T>(this.#identity).to(contactor).inSingletonScope();
    }

    get mediator() {
        return this.#container.get<T>(this.#identity);
    }
}