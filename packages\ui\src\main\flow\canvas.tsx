"use client";

import React, { useCallback, useState, useMemo, useEffect, useRef } from 'react';
import styled, { useTheme } from 'styled-components';

// ReactFlow imports
import ReactFlow, {
  Background,
  Controls,
  Panel,
  useReactFlow,
  useOnViewportChange,
  Viewport,
  Node,
  Edge,
  ProOptions,
  NodeMouseHandler,
  Connection,
  NodeChange,
  EdgeChange,
  NodeRemoveChange,
  EdgeRemoveChange,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { CanvasContainer, ZoomDisplay } from './canvasStyle';

// Custom components
import { TriggerNodeComponent, ActionNodeComponent } from './custom-nodes';
import { NodeDetailsView } from '../../node/NodeDetailsView';
import { autoLayoutNodes, centerNodes } from './autoLayout';

// 将 nodeTypes 定义在组件外部以避免ReactFlow警告
// 确保 nodeTypes 引用完全稳定
const NODE_TYPES = {
  triggerNode: TriggerNodeComponent,
  actionNode: ActionNodeComponent,
  // conditionNode: ConditionNodeComponent,
};

// 定义 proOptions 来隐藏归属标记
const proOptions: ProOptions = {
  hideAttribution: true,
};

export interface WorkflowCanvasProps {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  onConnect?: (connection: Connection) => void;
  onDrop?: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragOver?: (event: React.DragEvent<HTMLDivElement>) => void;
  onSelectionChange?: (elements: { nodes: Node[], edges: Edge[] }) => void;
  onNodeUpdate?: (nodeData: any) => void;
  // Add a prop to handle node double click and potentially fetch data in the parent
  onNodeDoubleClick?: (event: React.MouseEvent, node: Node) => void;
  // Add a prop to pass selected node details for the details view
  selectedNodeDetails?: any; // Assuming node details structure
  onNodeIdChange?: (oldId: string, newId: string) => void; // 新增ID变更回调
  nodeWidth?: number; // 节点宽度
  onAutoLayout?: (layoutedNodes: Node[]) => void; // 自动布局回调
  onCopyNodes?: (nodes: Node[]) => void; // 新增复制节点回调
  onPasteNodes?: () => void; // 新增粘贴节点回调
  // 🔧 添加最新的 nodesTestResultsMap 作为直接props，避免快照问题
  nodesTestResultsMap?: Record<string, any>;
  getLatestNodesTestResultsMap?: () => Record<string, any>;
  // 连接配置数据，由web层传入
  connectConfigs?: Array<{
    id: string;
    name: string;
    ctype: string;
    nodeinfo: Record<string, any>;
    description?: string;
  }>;
  // 连接配置查询回调，当NodeSettings需要特定类型的连接配置时调用
  onFetchConnectConfigs?: (ctype?: string) => Promise<Array<{
    id: string;
    name: string;
    ctype: string;
    nodeinfo: Record<string, any>;
    description?: string;
  }>>;
  // 表名获取回调，当SelectFilter需要获取数据库表名时调用
  onFetchTables?: (datasourceId: string) => Promise<{
    loading: boolean;
    error: string | null;
    tableOptions: Array<{ label: string; value: string; }>;
  }>;
}

export const WorkflowCanvas: React.FC<WorkflowCanvasProps> = React.memo(({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onDrop,
  onDragOver,
  onSelectionChange,
  onNodeUpdate,
  onNodeDoubleClick,
  selectedNodeDetails,
  onNodeIdChange,
  nodeWidth,
  onAutoLayout,
  onCopyNodes,
  onPasteNodes,
  // 🔧 接收最新的 nodesTestResultsMap 作为直接props
  nodesTestResultsMap,
  getLatestNodesTestResultsMap,
  // 连接配置相关props
  connectConfigs = [],
  onFetchConnectConfigs,
  // 表名获取相关props
  onFetchTables,
}) => {
  // ================ Hooks 和实例 ================
  const reactFlowInstance = useReactFlow();
  const theme = useTheme();

  // ================ 状态管理 ================
  const [isDragOver, setIsDragOver] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1); // 初始缩放设置为100%
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [nodeParameters, setNodeParameters] = useState<any[]>([]);
  // connectConfigs 现在通过props传入，不需要内部状态
  // Ref for ReactFlow wrapper
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  
  // 存储拖拽数据的引用
  const dragDataRef = useRef<string | null>(null);

  // ================ 优化的 nodeTypes 引用 ================
  // NODE_TYPES 已在组件外部定义，引用稳定，无需 memoization

  // ================ 优化的 nodes 和 edges ================
  const processedNodes = useMemo(() => {
    return nodes.map(node => {
      if (node.data && node.data.subflow) {
        return {
          ...node,
          data: {
            ...node.data,
            // 增加subflow标记，供自定义节点组件使用
            subflowLabel: node.data.subflow === true ? '子流程' : String(node.data.subflow)
          }
          // ====== 中文注释：移除subflow节点的边框样式 ======
          // 不再为subflow节点添加特殊边框，保持原有样式
          // style: {
          //   ...(node.style || {}),
          //   border: '2px solid #FF9800',
          //   boxShadow: '0 0 8px #FF980088'
          // }
        };
      }
      return node;
    });
  }, [nodes]);

  const processedEdges = useMemo(() => {
    // ====== 中文注释：对带有subflow的边做特殊显示 ======
    // edges属性处理：移除subflow的特殊样式，让所有边都显示为正常样式
    // 保留原有的基于输出连接点描述的标签显示
    // =============================
    return edges.map(edge => {
      // 移除subflow特殊样式，所有边都使用统一样式
      return edge;
    });
  }, [edges]);

  // 初始化ReactFlow实例
  useEffect(() => {
    if (reactFlowInstance) {
      // 只在初始化时设置缩放级别为100%，不强制重置
      const currentViewport = reactFlowInstance.getViewport();
      if (currentViewport.zoom === 1 && currentViewport.x === 0 && currentViewport.y === 0) {
        // 只有在默认状态时才设置，避免覆盖用户操作
        setZoomLevel(1);
      } else {
        // 同步当前的缩放级别
        setZoomLevel(currentViewport.zoom);
      }
    }
  }, [reactFlowInstance]); // 只依赖reactFlowInstance，移除zoomLevel依赖

  // 注释：缩放监听现在通过ReactFlow的onViewportChange回调处理，不需要额外的事件监听

  // 将 reactFlowInstance 暴露到全局变量，供拖拽操作使用
  useEffect(() => {
    if (reactFlowInstance) {
      (window as any).reactFlowInstance = reactFlowInstance;
    }
  }, [reactFlowInstance]);

  // ================ 事件处理函数 ================

  /**
   * 重置画布缩放到100%
   */
  const handleResetZoom = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.setViewport({ x: 0, y: 0, zoom: 1 });
      setZoomLevel(1);
    }
  }, [reactFlowInstance]);

  /**
   * 处理自动布局
   * 使用简单的水平排列布局算法
   */
  const handleAutoLayout = useCallback(() => {
    if (nodes.length === 0) return;

    // 获取画布容器尺寸
    const canvasElement = document.querySelector('.react-flow__viewport')?.parentElement;
    const canvasWidth = canvasElement?.offsetWidth || 1200;
    const canvasHeight = canvasElement?.offsetHeight || 800;

    // 使用新的水平排列布局
    const layoutedNodes = autoLayoutNodes(nodes, edges, {
      canvasWidth,
      canvasHeight,
      nodeHeight: 120, // 固定节点高度
    });

    // 通过回调更新节点位置
    if (onAutoLayout) {
      onAutoLayout(layoutedNodes);
    }

    // 自动布局后保持100%缩放
    setTimeout(() => {
      if (reactFlowInstance) {
        reactFlowInstance.setViewport({ x: 0, y: 0, zoom: 1 });
        setZoomLevel(1);
      }
    }, 100);
  }, [nodes, edges, onAutoLayout, reactFlowInstance]);

  /**
   * 处理拖拽悬停事件
   * 设置拖拽效果并显示投放区域
   */
  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    setIsDragOver(true);
    onDragOver?.(event);
  }, [onDragOver]);

  /**
   * 处理拖拽离开事件
   * 检查是否真正离开画布区域
   */
  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();

    const rect = event.currentTarget.getBoundingClientRect();
    const isStillInside = (
      event.clientX >= rect.left &&
      event.clientX <= rect.right &&
      event.clientY >= rect.top &&
      event.clientY <= rect.bottom
    );

    if (!isStillInside) {
      setIsDragOver(false);
    }
  }, []);

  /**
   * 处理节点投放事件
   * 完成从菜单到画布的节点拖拽
   */
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragOver(false);

    if (onDrop) {
      onDrop(event);
    }
  }, [onDrop]);

  /**
   * 全局拖拽事件监听
   * 捕获拖拽数据并处理拖拽结束
   */
  useEffect(() => {
    const handleGlobalDragStart = (event: DragEvent) => {
      // 存储拖拽数据
      const data = event.dataTransfer?.getData('application/reactflow');
      if (data) {
        dragDataRef.current = data;
      }
    };

    const handleGlobalDragEnd = (event: DragEvent) => {
      if (isDragOver && reactFlowWrapper) {
        const wrapper = document.querySelector('.react-flow');
        if (wrapper) {
          const rect = wrapper.getBoundingClientRect();
          const isInCanvas = (
            event.clientX >= rect.left &&
            event.clientX <= rect.right &&
            event.clientY >= rect.top &&
            event.clientY <= rect.bottom
          );

          if (isInCanvas && dragDataRef.current) {
            // 创建模拟的 dataTransfer 对象
            const mockDataTransfer = {
              getData: (format: string) => {
                if (format === 'application/reactflow') {
                  return dragDataRef.current || '';
                }
                return '';
              }
            };

            const syntheticEvent = {
              preventDefault: () => { },
              stopPropagation: () => { },
              dataTransfer: mockDataTransfer,
              clientX: event.clientX - 315,
              clientY: event.clientY,
              target: wrapper,
              currentTarget: wrapper
            } as unknown as React.DragEvent<HTMLDivElement>;

            if (onDrop) {
              onDrop(syntheticEvent);
            }
          }
        }
      }

      // 清理拖拽数据
      dragDataRef.current = null;
      setIsDragOver(false);
    };

    document.addEventListener('dragstart', handleGlobalDragStart);
    document.addEventListener('dragend', handleGlobalDragEnd);
    return () => {
      document.removeEventListener('dragstart', handleGlobalDragStart);
      document.removeEventListener('dragend', handleGlobalDragEnd);
    };
  }, [isDragOver, onDrop]);

  /**
   * 处理节点双击事件
   * 触发节点配置面板打开
   */
  const handleNodeDoubleClickInternal: NodeMouseHandler = useCallback((event, node) => {
    setSelectedNode(node);
    onNodeDoubleClick?.(event, node);
  }, [onNodeDoubleClick]);

  /**
   * 处理节点设置保存
   * 将配置数据传递给父组件
   */
  const handleNodeSettingsSave = (nodeData: any) => {
    if (onNodeUpdate) {
      onNodeUpdate(nodeData);
    }
  };

  /**
   * 处理关闭节点详情视图
   * 只关闭页面，不做任何数据保存操作
   */
  const handleNodeDetailsClose = () => {
    if (onNodeUpdate) {
      // 传递 null 表示关闭详情视图，不保存数据
      onNodeUpdate(null);
    }
  };

  /**
   * 处理键盘删除事件
   * 删除选中的节点和边，但在配置面板打开时禁用
   */
  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLDivElement>) => {
    // 如果NodeDetailsView打开，阻止所有快捷键操作
    if (selectedNodeDetails) {
      if (event.key === 'Delete' ||
        ((event.ctrlKey || event.metaKey) && event.key === 'c') ||
        ((event.ctrlKey || event.metaKey) && event.key === 'v')) {

        // 检查事件是否来自input、textarea、contenteditable或CodeMirror元素
        const target = event.target as HTMLElement;
        const isFromInputElement = target.tagName === 'INPUT' ||
          target.tagName === 'TEXTAREA' ||
          target.contentEditable === 'true' ||
          target.closest('input, textarea, [contenteditable="true"]') !== null ||
          target.closest('.cm-editor') !== null;

        if (isFromInputElement) {
          // 来自输入控件的事件，允许通过，不做任何处理
          return;
        } else {
          // 不是来自输入控件的事件，阻止画布快捷键操作
          event.preventDefault();
          event.stopPropagation();
          return;
        }
      }
    }

    // 复制操作检测
    if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
      const selectedNodes = nodes.filter(node => node.selected);

      if (onCopyNodes && selectedNodes.length > 0) {
        onCopyNodes(selectedNodes);
      }

      event.preventDefault();
      event.stopPropagation();
      return;
    }

    // 粘贴操作检测
    if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
      if (onPasteNodes) {
        onPasteNodes();
      }

      event.preventDefault();
      event.stopPropagation();
      return;
    }

    if (event.key === 'Delete') {
      const selectedNodes = nodes.filter(node => node.selected);
      const selectedEdges = edges.filter(edge => edge.selected);

      if (selectedNodes.length > 0 || selectedEdges.length > 0) {
        const removeChanges: (NodeRemoveChange | EdgeRemoveChange)[] = [];

        selectedNodes.forEach(node => {
          removeChanges.push({
            type: 'remove',
            id: node.id
          });
        });

        selectedEdges.forEach(edge => {
          removeChanges.push({
            type: 'remove',
            id: edge.id
          });
        });

        if (selectedNodes.length > 0) {
          onNodesChange(removeChanges.filter(change => selectedNodes.some(node => node.id === change.id)));
        }

        if (selectedEdges.length > 0) {
          onEdgesChange(removeChanges.filter(change => selectedEdges.some(edge => edge.id === change.id)));
        }
      }
    }
  }, [nodes, edges, onNodesChange, onEdgesChange, selectedNodeDetails, onCopyNodes, onPasteNodes]);

  const handleFocus = useCallback(() => {
    // Container gained focus
  }, []);

  const handleBlur = useCallback(() => {
    // Container lost focus  
  }, []);

  // ReactFlow初始化回调
  const handleInit = useCallback((instance: any) => {
    // 不强制设置缩放，只同步当前状态
    const { zoom } = instance.getViewport();
    setZoomLevel(zoom);
  }, []);

  // 使用useOnViewportChange hook监听视口变化
  useOnViewportChange({
    onChange: useCallback((viewport: Viewport) => {
      setZoomLevel(viewport.zoom);
    }, [])
  });


  // ReactFlow 键盘事件处理
  // const handleReactFlowKeyDown = useCallback((event: React.KeyboardEvent) => {
  //   // 🔍 调试日志 - ReactFlow键盘事件
  //   // console.log('🎯 REACTFLOW_KEY_DOWN:', {
  //   //   key: event.key,
  //   //   hasSelectedNodeDetails: !!selectedNodeDetails,
  //   //   target: event.target?.constructor?.name,
  //   //   timestamp: new Date().toISOString()
  //   // });

  //   // 如果配置面板打开，阻止删除键处理
  //   if (selectedNodeDetails && (event.key === 'Delete')) {
  //     // console.log('🚫 REACTFLOW_DELETE_BLOCKED:', {
  //     //   reason: 'selectedNodeDetails exists',
  //     //   key: event.key
  //     // });
  //     event.preventDefault();
  //     event.stopPropagation();
  //     return;
  //   }

  //   // 对于其他按键，阻止事件冒泡，避免与容器的键盘事件冲突
  //   event.stopPropagation();
  // }, [selectedNodeDetails]);

  // 检查是否有节点
  //const hasNodes = nodes.length > 0;

  // ================ 渲染前状态 ================
  return (
    <CanvasContainer
      className={isDragOver ? 'drag-over' : ''}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onKeyDown={handleKeyDown}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={0} // 允许容器获得焦点
    >
      <ReactFlow
        nodes={processedNodes}
        edges={processedEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDrop={handleDrop}
        onSelectionChange={onSelectionChange}
        onNodeDoubleClick={handleNodeDoubleClickInternal}
        nodeTypes={NODE_TYPES}
        proOptions={proOptions}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        minZoom={0.1}
        maxZoom={2}
        fitView={false}
        onInit={handleInit}
        deleteKeyCode={selectedNodeDetails ? null : 'Delete'}
        selectNodesOnDrag={false}
        multiSelectionKeyCode='Shift'
        panOnDrag={[0, 1, 2]}
        //selectionMode='partial'
        selectionKeyCode='Shift'
      >
        <Controls />
        <Background color="#aaa" gap={16} />
        <Panel position="bottom-left">
          <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
            <ZoomDisplay
              onClick={handleResetZoom}
              style={{ marginLeft: '30px', marginBottom: '-8px' }}
              className="zoom-display"
            >
              {Math.round((zoomLevel || 1) * 100)}%
            </ZoomDisplay>
            {nodes.length > 1 && (
              <ZoomDisplay
                onClick={handleAutoLayout}
                style={{ marginBottom: '-8px', backgroundColor: '#33C2EE', color: 'white' }}
                className="auto-layout-btn"
                title="自动整理布局"
              >
                自动布局
              </ZoomDisplay>
            )}
          </div>
        </Panel>
      </ReactFlow>

      {selectedNodeDetails && (
        <NodeDetailsView
          node={selectedNodeDetails.node}
          parameters={selectedNodeDetails.parameters || []}
          savedValues={selectedNodeDetails.savedValues || {}}
          onClose={handleNodeDetailsClose}
          onSave={handleNodeSettingsSave}
          onNodeIdChange={onNodeIdChange}
          nodeWidth={nodeWidth}
          onTest={selectedNodeDetails.onTest}
          onTestPreviousNode={selectedNodeDetails.onTestPreviousNode}
          onSaveMockData={selectedNodeDetails.onSaveMockData}
          testOutput={selectedNodeDetails.testOutput}
          lastTestResult={selectedNodeDetails.lastTestResult}
          previousNodeIds={selectedNodeDetails.previousNodeIds}
          onPreviousNodeChange={selectedNodeDetails.onPreviousNodeChange}
          selectedPreviousNodeId={selectedNodeDetails.selectedPreviousNodeId}
          nodesTestResultsMap={nodesTestResultsMap || selectedNodeDetails.nodesTestResultsMap}
          getLatestNodesTestResultsMap={getLatestNodesTestResultsMap || selectedNodeDetails.getLatestNodesTestResultsMap}
          nodesDetailsMap={selectedNodeDetails.nodesDetailsMap}
          showToast={selectedNodeDetails.showToast}
          connectConfigs={connectConfigs}
          onFetchConnectConfigs={onFetchConnectConfigs}
          onFetchTables={onFetchTables}
        />
      )}
    </CanvasContainer>
  );
});

WorkflowCanvas.displayName = 'WorkflowCanvas';