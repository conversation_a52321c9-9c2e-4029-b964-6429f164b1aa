/**
 * WorkflowEditor 组件集成测试
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ReactFlowProvider } from 'reactflow';
import { ThemeProvider } from 'styled-components';
import { WorkflowEditor } from '../../components/WorkflowEditor/WorkflowEditor';

// Mock WorkflowContext
const mockWorkflowContext = {
  nodes: [],
  edges: [],
  workflowId: 'test-workflow',
  nodesDetailsMap: {},
  nodesTestResultsMap: {},
  updateNodeDetails: jest.fn(),
  updateNodeTestResult: jest.fn(),
  deleteNodeCompletely: jest.fn()
};

jest.mock('@/contexts/WorkflowContext', () => ({
  useWorkflow: () => mockWorkflowContext
}));

// Mock UI components
jest.mock('@repo/ui/main/flow', () => ({
  WorkflowCanvas: ({ children, ...props }: any) => (
    <div data-testid="workflow-canvas" {...props}>
      {children}
    </div>
  )
}));

// Mock theme
const mockTheme = {
  colors: {
    primary: '#1890ff',
    textPrimary: '#333',
    border: '#e1e5e9'
  }
};

// Test wrapper
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={mockTheme}>
    <ReactFlowProvider>
      {children}
    </ReactFlowProvider>
  </ThemeProvider>
);

describe('WorkflowEditor 组件', () => {
  const defaultProps = {
    onMenuCollapseChange: jest.fn(),
    showError: jest.fn(),
    showWarning: jest.fn(),
    onCanvasStateChange: jest.fn(),
    onFetchConnectConfigs: jest.fn(),
    onFetchTables: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染', () => {
    render(
      <TestWrapper>
        <WorkflowEditor {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByTestId('workflow-canvas')).toBeInTheDocument();
  });

  it('应该处理画布状态变化', async () => {
    render(
      <TestWrapper>
        <WorkflowEditor {...defaultProps} />
      </TestWrapper>
    );

    const canvas = screen.getByTestId('workflow-canvas');
    
    // 模拟画布状态变化
    if (canvas && defaultProps.onCanvasStateChange) {
      fireEvent.change(canvas);
      
      await waitFor(() => {
        // 验证状态变化回调被调用
        // 这里需要根据实际实现调整
      });
    }
  });

  it('应该处理工作流ID变化', () => {
    const { rerender } = render(
      <TestWrapper>
        <WorkflowEditor {...defaultProps} />
      </TestWrapper>
    );

    // 更改工作流ID
    mockWorkflowContext.workflowId = 'new-workflow-id';

    rerender(
      <TestWrapper>
        <WorkflowEditor {...defaultProps} />
      </TestWrapper>
    );

    // 验证组件正确处理了ID变化
    expect(screen.getByTestId('workflow-canvas')).toBeInTheDocument();
  });

  it('应该处理错误情况', () => {
    // 模拟错误情况
    const errorProps = {
      ...defaultProps,
      onFetchConnectConfigs: jest.fn().mockRejectedValue(new Error('网络错误'))
    };

    render(
      <TestWrapper>
        <WorkflowEditor {...errorProps} />
      </TestWrapper>
    );

    // 验证错误处理
    expect(screen.getByTestId('workflow-canvas')).toBeInTheDocument();
  });
});