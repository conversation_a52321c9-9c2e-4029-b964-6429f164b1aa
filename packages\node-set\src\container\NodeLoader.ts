// 节点加载器，使用fast-glob来动态加载所有节点
import 'reflect-metadata';
import { Container } from 'inversify';
import glob from 'fast-glob';
import path from 'path';
import { createRequire } from 'module';
import { INode, AbstractNodeLoader, NodeRegistry, Symbols } from '@repo/common';

// 创建一个指向项目根目录的 require 函数
const projectRequire = createRequire(process.cwd() + '/index.js');

//const projectRequire = createRequire(process.cwd() + '/package.json');

// 扩展 NodeJS 的 Module 类型
declare module 'module' {
  interface Module {
    _nodeModulePaths: (from: string) => string[];
    _resolveFilename: (request: string, parent: any, isMain: boolean, options: any) => string;
  }
}

/**
 * 具体的文件系统节点加载器实现
 * 专门用于从 node-set 包中加载节点
 */
export class FileSystemNodeLoader extends AbstractNodeLoader {
  private container: Container;
  private nodeSetRoot: string;

  constructor(serviceContainer?: Container) {
    // 创建依赖注入容器和 NodeRegistry
    const container = new Container();
    container.bind<NodeRegistry>(Symbols.NodeRegistry).to(NodeRegistry).inSingletonScope();
    
    // 如果提供了外部服务容器，将其绑定到当前容器
    if (serviceContainer) {
      container.parent = serviceContainer;
    }
    
    const nodeRegistry = container.get<NodeRegistry>(Symbols.NodeRegistry);
    
    super(nodeRegistry);
    this.container = container;
    
    // 设置 node-set 包的根目录
    this.nodeSetRoot = this.resolveNodeSetRoot();
  }

  private resolveNodeSetRoot(): string {
    try {
      // 使用 __dirname 来定位当前文件位置
      const currentDir = __dirname;

      // 验证目录是否正确
      let fs;
      try {
        fs = eval('require')('fs'); // 使用 eval 来避免 Next.js 打包时的模块替换
      } catch (fsError) {
        console.warn('Could not load fs module:', fsError);
        // 如果无法加载 fs，使用默认路径
        return path.resolve(process.cwd(), '../packages/node-set');
      }

      // 在Next.js环境中，__dirname可能是虚拟路径，所以直接尝试可能的位置
      const possibleRoots = [
        // 从当前工作目录开始查找
        path.resolve(process.cwd(), '../packages/node-set'),
        path.resolve(process.cwd(), 'packages/node-set'),
        path.resolve(process.cwd(), '../../packages/node-set'),
        // 如果__dirname是真实路径，尝试从它开始解析
        path.resolve(currentDir, '../../..'),
        // 其他可能的位置
        path.resolve(process.cwd(), '../node-set'),
        path.resolve(process.cwd(), '../../node-set')
      ];

      for (const root of possibleRoots) {
        const pkgJsonPath = path.join(root, 'package.json');
        if (fs.existsSync(pkgJsonPath)) {
          return root;
        }
      }
      
      throw new Error('Could not find package.json in any of the expected locations');
    } catch (e) {
      console.error('Failed to resolve package path:', e);
      const fallback = path.resolve(process.cwd(), '../packages/node-set');
      return fallback;
    }
  }

  private setupModuleResolution(): void {
    try {
      const Module = eval('require')('module'); // 使用 eval 来避免 Next.js 打包时的模块替换
      const originalResolve = Module._resolveFilename;

      // 重写模块解析逻辑
      Module._resolveFilename = (request: string, parent: any, isMain: boolean, options: any): string => {
        if (request === '@repo/common') {
          // 尝试从多个可能的位置解析 @repo/common
          const possiblePaths = [
            path.resolve(this.nodeSetRoot, '../../node_modules/@repo/common/dist/index.js'),
            path.resolve(this.nodeSetRoot, '../../packages/common/dist/index.js'),
            path.resolve(process.cwd(), 'node_modules/@repo/common/dist/index.js'),
            path.resolve(process.cwd(), 'packages/common/dist/index.js')
          ];

          for (const possiblePath of possiblePaths) {
            try {
              return projectRequire.resolve(possiblePath);
            } catch (e) {
              // 继续尝试下一个路径
            }
          }
        }

        // 对于其他模块，使用原始解析逻辑
        return originalResolve.call(this, request, parent, isMain, options);
      };
    } catch (error) {
      console.warn('Failed to setup module resolution, falling back to default behavior:', error);
      // 如果模块解析设置失败，就跳过这一步，使用默认的模块解析
    }
  }

  async loadNodes(): Promise<void> {
    try {      
      this.setupModuleResolution();
      
      const nodesDir = path.join(this.nodeSetRoot, 'dist', 'nodes');      
      // 检查目录是否存在
      let fs;
      try {
        fs = eval('require')('fs'); // 使用 eval 来避免 Next.js 打包时的模块替换
      } catch (fsError) {
        console.warn('⚠️ [NodeLoader] Could not load fs module:', fsError);
        // 如果无法加载 fs，跳过目录检查，直接尝试加载
      }

      if (fs) {
        if (!fs.existsSync(nodesDir)) {
          console.error(`❌ [NodeLoader] Nodes directory does not exist: ${nodesDir}`);
          // 创建目录
          try {
            fs.mkdirSync(nodesDir, { recursive: true });
          } catch (mkdirError) {
            console.warn('⚠️ [NodeLoader] Could not create nodes directory:', mkdirError);
          }
        } else {
        }

        // 检查源码目录
        const srcNodesDir = path.join(this.nodeSetRoot, 'src', 'nodes');
        if (fs.existsSync(srcNodesDir)) {
          const srcContents = fs.readdirSync(srcNodesDir);
        } 
        
        // 检查编译后目录
        if (fs.existsSync(nodesDir)) {
          const distContents = fs.readdirSync(nodesDir);
          
          // 检查每个子目录
          for (const item of distContents) {
            const itemPath = path.join(nodesDir, item);
            if (fs.statSync(itemPath).isDirectory()) {
              const nodeJsPath = path.join(itemPath, 'node.js');
            }
          }
        } 
      }
      
      const nodeFiles = await glob(['**/node.js'], {
        cwd: nodesDir,
        absolute: true,
        ignore: ['**/node_modules/**'],
      });
            
      // 遍历并加载每个节点文件
      for (const filePath of nodeFiles) {
        await this.loadNodeFile(filePath);
      }
      
    } catch (error) {
      // 记录整体初始化失败
      if (error instanceof Error) {
        console.error('❌ [NodeLoader] 节点加载器初始化失败:', error.message);
      }
      throw error; // 重新抛出错误，因为这是致命错误
    }
  }

  private async loadNodeFile(filePath: string): Promise<void> {
    try {
      
      // 使用 eval 和 require 动态加载节点模块，避免 Next.js 打包问题
      const nodeModule = eval('require')(filePath);

      // 过滤出符合 INode 接口的导出类
      const exportedClasses = Object.values(nodeModule).filter(
        (exp): exp is new () => INode =>
          typeof exp === 'function' &&
          exp.prototype &&
          this.isValidNodeClass(exp as new () => any)
      );
            
      if (exportedClasses.length === 0) {
        console.warn(`⚠️ [NodeLoader] No valid node classes found in ${filePath}`);
        // 检查每个导出的详细信息
      }

      // 将找到的节点类注册到容器中
      for (const NodeClass of exportedClasses) {
        await this.registerNodeClass(NodeClass, filePath);
      }
    } catch (error) {
      // 记录单个节点加载失败，但继续处理其他节点
      if (error instanceof Error) {
        console.error(`❌ [NodeLoader] 节点加载失败 (${filePath}):`, error.message);
      }
    }
  }

  private isValidNodeClass(NodeClass: new () => any): boolean {
    try {
      const instance = new NodeClass();
      return 'detail' in instance && 'node' in instance;
    } catch {
      return false;
    }
  }

  private async registerNodeClass(NodeClass: new () => INode, filePath: string): Promise<void> {
    try {
      // 为每个节点类创建唯一的标识符
      const nodeSymbol = Symbol(`Node_${NodeClass.name}_${Date.now()}`);
      
      // 将节点类绑定到容器，使用唯一标识符
      this.container.bind<INode>(nodeSymbol).to(NodeClass);
      
      // 从容器中获取节点实例，这样可以自动注入依赖
      const instance = this.container.get<INode>(nodeSymbol);
       
      if (!instance.detail) {
        console.warn(`⚠️ [NodeLoader] Skipping node class ${NodeClass.name}: missing required 'detail' property`);
        return;
      }
      
      if (!instance.node) {
        console.warn(`⚠️ [NodeLoader] Skipping node class ${NodeClass.name}: missing required 'node' property`);
        return;
      }
      
      console.log(`✅ [NodeLoader] Successfully registered node: ${instance.node.name} (${instance.node.kind})`);
      
      // 使用 nodeRegistry 注册节点实例
      this.nodeRegistry.registerNode(instance);
    } catch (error) {
      console.error(`❌ [NodeLoader] Failed to register node class ${NodeClass.name || 'unknown'} from ${filePath}:`, error);
      if (error instanceof Error) {
        console.error(`📍 [NodeLoader] Registration error stack:`, error.stack);
      }
    }
  }
}

// 为了向后兼容，保留原来的 NodeLoader 类名
export class NodeLoader extends FileSystemNodeLoader {
  constructor(serviceContainer?: Container) {
    super(serviceContainer);
  }
}
