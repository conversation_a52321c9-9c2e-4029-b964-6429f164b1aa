/**
 * 工作流画布组件 - 应用层包装组件
 * 
 * 这个组件是对packages/ui/src/main/flow/canvas.tsx的应用层包装
 * 主要负责：
 * 1. 集成应用层的业务逻辑
 * 2. 处理应用特定的事件和状态
 * 3. 提供应用层的数据转换和适配
 */

import React, { useCallback, useMemo } from 'react';
import { Node, Edge, Connection, NodeChange, EdgeChange } from 'reactflow';

// 导入UI层的WorkflowCanvas组件
import { WorkflowCanvas as UIWorkflowCanvas } from '@repo/ui/main/flow';

// 导入应用层类型
import { NodeDetails, ConnectConfig, FetchTablesResponse } from '../../types/node';

// 导入工具函数
import { logger } from '../../utils/errorHandling';

interface AppWorkflowCanvasProps {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  onConnect?: (connection: Connection) => void;
  onDrop?: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragOver?: (event: React.DragEvent<HTMLDivElement>) => void;
  onSelectionChange?: (elements: { nodes: Node[], edges: Edge[] }) => void;
  onNodeUpdate?: (nodeData: any) => void;
  onNodeDoubleClick?: (event: React.MouseEvent, node: Node) => void;
  selectedNodeDetails?: any;
  onNodeIdChange?: (oldId: string, newId: string) => void;
  nodeWidth?: number;
  onAutoLayout?: (layoutedNodes: Node[]) => void;
  onCopyNodes?: (nodes: Node[]) => void;
  onPasteNodes?: () => void;
  nodesTestResultsMap?: Record<string, any>;
  getLatestNodesTestResultsMap?: () => Record<string, any>;
  connectConfigs?: ConnectConfig[];
  onFetchConnectConfigs?: (ctype?: string) => Promise<ConnectConfig[]>;
  onFetchTables?: (datasourceId: string, search?: string) => Promise<FetchTablesResponse>;
}

/**
 * 应用层工作流画布组件
 */
export const WorkflowCanvas: React.FC<AppWorkflowCanvasProps> = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onDrop,
  onDragOver,
  onSelectionChange,
  onNodeUpdate,
  onNodeDoubleClick,
  selectedNodeDetails,
  onNodeIdChange,
  nodeWidth,
  onAutoLayout,
  onCopyNodes,
  onPasteNodes,
  nodesTestResultsMap,
  getLatestNodesTestResultsMap,
  connectConfigs = [],
  onFetchConnectConfigs,
  onFetchTables
}) => {

  /**
   * 处理连接事件 - 添加应用层逻辑
   */
  const handleConnect = useCallback((connection: Connection) => {
    logger.debug('应用层处理连接', { connection });
    
    // 这里可以添加应用层特定的连接验证逻辑
    // 例如：业务规则验证、权限检查等
    
    if (onConnect) {
      onConnect(connection);
    }
  }, [onConnect]);

  /**
   * 处理拖拽放置 - 添加应用层逻辑
   */
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    logger.debug('应用层处理拖拽放置');
    
    // 这里可以添加应用层特定的拖拽逻辑
    // 例如：节点创建规则、位置计算等
    
    if (onDrop) {
      onDrop(event);
    }
  }, [onDrop]);

  /**
   * 处理节点双击 - 添加应用层逻辑
   */
  const handleNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
    logger.debug('应用层处理节点双击', { nodeId: node.id });
    
    // 这里可以添加应用层特定的双击逻辑
    // 例如：权限检查、节点状态验证等
    
    if (onNodeDoubleClick) {
      onNodeDoubleClick(event, node);
    }
  }, [onNodeDoubleClick]);

  /**
   * 处理选择变化 - 添加应用层逻辑
   */
  const handleSelectionChange = useCallback((elements: { nodes: Node[], edges: Edge[] }) => {
    logger.debug('应用层处理选择变化', { 
      nodeCount: elements.nodes.length,
      edgeCount: elements.edges.length 
    });
    
    // 这里可以添加应用层特定的选择逻辑
    // 例如：选择状态同步、工具栏更新等
    
    if (onSelectionChange) {
      onSelectionChange(elements);
    }
  }, [onSelectionChange]);

  /**
   * 转换连接配置格式以适配UI层
   */
  const uiConnectConfigs = useMemo(() => {
    return connectConfigs.map(config => ({
      id: config.id,
      name: config.name,
      ctype: config.ctype,
      nodeinfo: config.nodeinfo,
      description: config.description
    }));
  }, [connectConfigs]);

  /**
   * 包装获取连接配置函数以适配UI层
   */
  const handleFetchConnectConfigs = useCallback(async (ctype?: string) => {
    if (!onFetchConnectConfigs) return [];
    
    try {
      const configs = await onFetchConnectConfigs(ctype);
      logger.debug('应用层获取连接配置成功', { ctype, count: configs.length });
      
      // 转换格式以适配UI层
      return configs.map(config => ({
        id: config.id,
        name: config.name,
        ctype: config.ctype,
        nodeinfo: config.nodeinfo,
        description: config.description
      }));
    } catch (error) {
      logger.error('应用层获取连接配置失败', error);
      return [];
    }
  }, [onFetchConnectConfigs]);

  /**
   * 包装获取表名函数以适配UI层
   */
  const handleFetchTables = useCallback(async (datasourceId: string, search?: string) => {
    if (!onFetchTables) {
      return {
        loading: false,
        error: '获取表名功能未实现',
        tableOptions: []
      };
    }
    
    try {
      const result = await onFetchTables(datasourceId, search);
      logger.debug('应用层获取表名成功', { 
        datasourceId, 
        search, 
        count: result.tableOptions.length 
      });
      
      return result;
    } catch (error) {
      logger.error('应用层获取表名失败', error);
      return {
        loading: false,
        error: error instanceof Error ? error.message : '获取表名失败',
        tableOptions: []
      };
    }
  }, [onFetchTables]);


  
  return (
    <UIWorkflowCanvas
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={handleConnect}
      onDrop={handleDrop}
      onDragOver={onDragOver}
      onSelectionChange={handleSelectionChange}
      onNodeUpdate={onNodeUpdate}
      onNodeDoubleClick={handleNodeDoubleClick}
      selectedNodeDetails={selectedNodeDetails}
      onNodeIdChange={onNodeIdChange}
      nodeWidth={nodeWidth}
      onAutoLayout={onAutoLayout}
      onCopyNodes={onCopyNodes}
      onPasteNodes={onPasteNodes}
      nodesTestResultsMap={nodesTestResultsMap}
      getLatestNodesTestResultsMap={getLatestNodesTestResultsMap}
      connectConfigs={uiConnectConfigs}
      onFetchConnectConfigs={handleFetchConnectConfigs}
      onFetchTables={handleFetchTables}
    />
  );
};

export default WorkflowCanvas;