import { NextResponse } from "next/server";
import { initializeNodes, getAllCategories } from "@repo/node-set";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const kind = searchParams.get('kind');
    console.log('🔍 [API] /api/nodes 请求参数:', { kind });

    console.log('🔍 [API] 开始初始化节点...');
    const nodeRegistry = await initializeNodes();
    console.log('🔍 [API] 节点初始化完成');
    
    if (kind) {
      // If kind is provided, return the specific node
      const node = nodeRegistry.getNodeByKind(kind);
      if (node) {
        console.log('🔍 [API] 返回特定节点:', node);
        return NextResponse.json({ node });
      } else {
        console.log('🔍 [API] 未找到节点:', kind);
        return NextResponse.json(
          { message: `Node with kind ${kind} not found` },
          { status: 404 }
        );
      }
    } else {
      // If no kind is provided, return all categorized nodes
      console.log('🔍 [API] 获取所有分类...');
      const categories = getAllCategories();
      console.log('🔍 [API] 分类数量:', categories.length);
      console.log('🔍 [API] 分类列表:', categories.map(c => ({ name: c.name, id: c.id })));
      
      const nodesByCategory = nodeRegistry.getNodesByCategoryNotWithDetail();
      console.log('🔍 [API] 按分类获取的节点:', nodesByCategory);

      // Validate that we have nodes loaded
      if (Object.keys(nodesByCategory).length === 0) {
        console.warn('🔍 [API] 警告: 没有加载到任何节点，请检查节点注册是否有错误');
      }

      // Build complete category and node association data
      const result = categories.map(category => {
        const categoryNodes = nodesByCategory[category.id] || [];
        console.log(`🔍 [API] 分类 "${category.name}" (${category.id}) 的节点数量:`, categoryNodes.length);
        console.log(`🔍 [API] 分类 "${category.name}" 的节点:`, categoryNodes.map(n => ({ kind: n.kind, name: n.name })));
        
        return {
          ...category,
          nodes: categoryNodes
        };
      });

      console.log('🔍 [API] 最终返回的数据源:', {
        categoriesCount: result.length,
        totalNodes: result.reduce((sum, cat) => sum + cat.nodes.length, 0)
      });

      // Add cache control headers
      const response = NextResponse.json({ dataSource: result });
      // response.headers.set('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
      return response;
    }
  } catch (error) {
    console.error('🔍 [API] 错误: Failed to get node information:', error);
    return NextResponse.json(
      {
        message: "Failed to get node information",
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}