import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@repo/db';
import mysql from 'mysql2/promise';

/**
 * GET /api/database/tables/[id]
 * 获取指定数据库连接的所有表名
 * 路径参数:
 * - id: 连接配置ID
 * 查询参数:
 * - search?: 搜索关键词（可选）
 */
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = params;
        const { searchParams } = new URL(request.url);
        const search = searchParams.get('search') || '';

        // 获取连接配置
        const connectConfig = await prisma.connectConfig.findUnique({
            where: { id }
        });

        if (!connectConfig) {
            return NextResponse.json(
                {
                    success: false,
                    error: '连接配置不存在',
                    message: `连接配置 "${id}" 不存在`
                },
                { status: 404 }
            );
        }

        // 检查是否为MySQL连接
        if (connectConfig.ctype !== 'mysql') {
            return NextResponse.json(
                {
                    success: false,
                    error: '不支持的数据库类型',
                    message: `数据库类型 "${connectConfig.ctype}" 不支持表名查询`
                },
                { status: 400 }
            );
        }

        // 解析连接配置
        const configData = JSON.parse(connectConfig.configinfo);

        const connectionConfig = {
            host: configData.host || 'localhost',
            port: configData.port || 3306,
            database: configData.database,
            user: configData.username || configData.user,
            password: configData.password || '',
            connectTimeout: (configData.connectionTimeout || 30) * 1000,
            ssl: configData.ssl || false
        };

        // 创建数据库连接
        const connection = await mysql.createConnection(connectionConfig);

        try {
            // 查询表名
            let query = 'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE table_schema = ?';
            const values = [configData.database];

            // 如果有搜索关键词，添加过滤条件
            if (search) {
                query += ' AND TABLE_NAME LIKE ?';
                values.push(`%${search}%`);
            }

            query += ' ORDER BY TABLE_NAME';

            const [rows] = await connection.execute(query, values);

            // 格式化结果
            const tables = (rows as any[]).map((row) => ({
                value: row.TABLE_NAME || row.table_name,
                label: row.TABLE_NAME || row.table_name
            }));

            return NextResponse.json({
                success: true,
                data: {
                    tables,
                    total: tables.length,
                    search: search || null
                },
                message: `成功获取 ${tables.length} 个表`
            });

        } finally {
            // 关闭连接
            await connection.end();
        }

    } catch (error) {
        console.error(`获取表名失败 [${params.id}]:`, error);
        return NextResponse.json(
            {
                success: false,
                error: '获取表名失败',
                message: error instanceof Error ? error.message : '未知错误'
            },
            { status: 500 }
        );
    }
}