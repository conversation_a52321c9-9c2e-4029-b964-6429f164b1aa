import { NextResponse } from "next/server";
import { initializeNodes } from "@repo/node-set";

// GET /api/nodes/[nodeID] - 获取特定节点的详细信息
export async function GET(
  request: Request,
  { params }: { params: Promise<{ nodeID: string }> }
) {
  try {
    const resolvedParams = await params; // await params
    console.log('params', resolvedParams);
    const kind = resolvedParams.nodeID; // Use nodeID from URL params but treat it as kind
    const nodeRegistry = await initializeNodes();
    const node = nodeRegistry.getNodeByKind(kind);
    
    if (!node) {
      return NextResponse.json(
        { message: `未找到kind为 "${kind}" 的节点` },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ node: node.detail });
  } catch (error) {
    console.error("获取节点详情失败:", error);
    return NextResponse.json(
      { message: "获取节点详情失败" },
      { status: 500 }
    );
  }
}