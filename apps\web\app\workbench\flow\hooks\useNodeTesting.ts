/**
 * 节点测试相关的自定义hooks
 */

import { useCallback, useState, useRef } from 'react';
import { Edge } from 'reactflow';
import { NodeDetails, NodeTestResult, NodeExecutionContext } from '../types/node';
import { getAllPreviousNodeIds, extractTemplateVariables, canNodeExecute } from '../utils/nodeProcessing';
import { handleAsyncOperation, logger } from '../utils/errorHandling';
import { debugNode } from '@/services/nodeDebugService';
import { testWorkflow } from '@/services/workflowTestService';
import { 
  API_ENDPOINTS, 
  TEST_TIMEOUT, 
  TEST_RETRY_COUNT, 
  NODE_ERROR_MESSAGES, 
  SUCCESS_MESSAGES,
  WORKFLOW_ERROR_MESSAGES 
} from '../utils/constants';

interface UseNodeTestingProps {
  workflowId: string;
  nodesDetailsMap: Record<string, NodeDetails>;
  nodesTestResultsMap: Record<string, NodeTestResult>;
  edgesState: Edge[];
  updateNodeTestResult: (nodeId: string, result: NodeTestResult) => void;
  updateNodeDetails: (nodeId: string, details: Partial<NodeDetails>) => void;
  showError: (title: string, message: string) => void;
  showSuccess: (title: string, message: string) => void;
  showWarning: (title: string, message: string) => void;
}

export const useNodeTesting = ({
  workflowId,
  nodesDetailsMap,
  nodesTestResultsMap,
  edgesState,
  updateNodeTestResult,
  updateNodeDetails,
  showError,
  showSuccess,
  showWarning
}: UseNodeTestingProps) => {
  const [isTestingWorkflow, setIsTestingWorkflow] = useState(false);
  const [testingNodes, setTestingNodes] = useState<Set<string>>(new Set());
  
  // 使用ref来获取最新的测试结果映射，避免闭包问题
  const nodesTestResultsMapRef = useRef(nodesTestResultsMap);
  nodesTestResultsMapRef.current = nodesTestResultsMap;

  const getLatestNodesTestResultsMap = useCallback(() => {
    return nodesTestResultsMapRef.current;
  }, []);

  /**
   * 测试单个节点
   */
  const handleNodeTest = useCallback(async (
    nodeValues: Record<string, any>, 
    nodeInstanceId: string
  ) => {
    if (testingNodes.has(nodeInstanceId)) {
      showWarning('测试进行中', '该节点正在测试中，请稍候');
      return;
    }

    setTestingNodes(prev => new Set(prev).add(nodeInstanceId));

    const result = await handleAsyncOperation(async () => {
      logger.info('开始节点测试', { nodeInstanceId, nodeValues: Object.keys(nodeValues) });

      // 获取节点详情
      const nodeDetails = nodesDetailsMap[nodeInstanceId];
      if (!nodeDetails || !nodeDetails.nodeInfo) {
        throw new Error(`${NODE_ERROR_MESSAGES.NODE_NOT_FOUND}: ${nodeInstanceId}`);
      }

      const node = nodeDetails.nodeInfo;
      const nodeKind = nodeDetails.originalNodeKind || node.data.kind;

      // 检查节点是否可以执行
      const previousNodeIds = getAllPreviousNodeIds(nodeInstanceId, edgesState, nodesDetailsMap);
      const canExecute = canNodeExecute(nodeInstanceId, nodeDetails, previousNodeIds, nodesTestResultsMap);
      
      if (!canExecute) {
        throw new Error(NODE_ERROR_MESSAGES.NODE_EXECUTION_FAILED);
      }

      // 提取模板变量
      const templateNodeIds = extractTemplateVariables(nodeValues);
      const hasTemplateVariables = templateNodeIds.length > 0;

      // 构造调试请求
      const actions = [];

      // 添加所有前置节点到actions
      previousNodeIds.forEach(prevNodeId => {
        const prevNodeDetails = nodesDetailsMap[prevNodeId];
        if (prevNodeDetails && prevNodeDetails.nodeInfo) {
          const prevNode = prevNodeDetails.nodeInfo;
          actions.push({
            id: prevNodeId,
            inputs: {
              ...(prevNodeDetails.savedValues || {}),
              id: prevNodeId
            },
            kind: prevNodeDetails.originalNodeKind || prevNode.data.kind,
            nodeName: prevNode.data.name || prevNodeId,
            position: prevNode.position,
            type: prevNode.type || 'triggerNode'
          });
        }
      });

      // 添加当前测试节点
      actions.push({
        id: nodeInstanceId,
        inputs: { ...nodeValues, id: nodeInstanceId },
        kind: nodeKind,
        nodeName: node.data.name || nodeInstanceId,
        position: node.position,
        type: node.type || 'triggerNode'
      });

      // 构建edges
      const edges = [];
      const allNodeIds = [...previousNodeIds, nodeInstanceId];

      // 找到第一个节点
      const firstNodeId = allNodeIds.find(nodeId =>
        !edgesState.some(edge => edge.target === nodeId && allNodeIds.includes(edge.source))
      );

      // 从$source到第一个节点的连接
      if (firstNodeId) {
        edges.push({
          from: "$source",
          to: firstNodeId
        });
      }

      // 添加节点之间的连接
      edgesState.forEach(edge => {
        if (allNodeIds.includes(edge.source) && allNodeIds.includes(edge.target)) {
          edges.push({
            from: edge.source,
            to: edge.target,
            sourceHandle: edge.sourceHandle || 'right-0',
            targetHandle: edge.targetHandle || 'left-0',
            label: edge.label || ''
          });
        }
      });

      const debugRequest: any = {
        actions,
        edges
      };

      // 如果包含模板变量，添加state字段
      if (hasTemplateVariables) {
        const state: Record<string, any> = {};
        const latestResults = getLatestNodesTestResultsMap();

        templateNodeIds.forEach(templateNodeId => {
          const testResult = latestResults[templateNodeId];
          if (testResult && testResult.success && testResult.data) {
            state[templateNodeId] = testResult.data;
          }
        });

        debugRequest.state = state;
        logger.debug('添加模板变量状态', { templateNodeIds, state });
      }

      logger.debug('发送节点调试请求', { debugRequest });

      // 发送调试请求
      const debugResult = await debugNode(debugRequest);

      // 更新测试结果
      const testResult: NodeTestResult = {
        success: true,
        data: debugResult,
        timestamp: Date.now(),
        nodeId: nodeInstanceId
      };

      updateNodeTestResult(nodeInstanceId, testResult);

      logger.info('节点测试成功', { nodeInstanceId, result: debugResult });

      return debugResult;
    }, `节点 ${nodeInstanceId} 测试失败`);

    setTestingNodes(prev => {
      const newSet = new Set(prev);
      newSet.delete(nodeInstanceId);
      return newSet;
    });

    if (result.success) {
      showSuccess('成功', SUCCESS_MESSAGES.NODE_TESTED);
    } else {
      showError('出错:', result.error);
      
      // 记录失败的测试结果
      const failedResult: NodeTestResult = {
        success: false,
        error: result.error,
        timestamp: Date.now(),
        nodeId: nodeInstanceId
      };
      updateNodeTestResult(nodeInstanceId, failedResult);
    }

    return result;
  }, [
    nodesDetailsMap,
    edgesState,
    nodesTestResultsMap,
    testingNodes,
    updateNodeTestResult,
    getLatestNodesTestResultsMap,
    showError,
    showSuccess,
    showWarning
  ]);

  /**
   * 左侧面板节点测试（测试前置节点）
   */
  const handleLeftPanelNodeTest = useCallback(async (
    nodeValues: Record<string, any>, 
    nodeInstanceId: string
  ) => {
    return handleNodeTest(nodeValues, nodeInstanceId);
  }, [handleNodeTest]);

  /**
   * 测试整个工作流
   */
  const handleWorkflowTest = useCallback(async () => {
    if (!workflowId) {
      showError('出错:', WORKFLOW_ERROR_MESSAGES.MISSING_WORKFLOW_ID);
      return;
    }

    if (isTestingWorkflow) {
      showWarning('测试进行中', '工作流正在测试中，请稍候');
      return;
    }

    setIsTestingWorkflow(true);

    const result = await handleAsyncOperation(async () => {
      logger.info('开始工作流测试', { workflowId });

      const testResult = await testWorkflow(workflowId);

      logger.info('工作流测试成功', { workflowId, result: testResult });

      return testResult;
    }, WORKFLOW_ERROR_MESSAGES.TEST_FAILED);

    setIsTestingWorkflow(false);

    if (result.success) {
      showSuccess('成功', `${SUCCESS_MESSAGES.WORKFLOW_TESTED}\n返回结果: ${JSON.stringify(result.data, null, 2)}`);
    } else {
      showError('出错:', result.error);
    }

    return result;
  }, [workflowId, isTestingWorkflow, showError, showSuccess, showWarning]);

  /**
   * 保存模拟测试数据
   */
  const handleSaveMockData = useCallback((
    mockTestResult: any, 
    nodeInstanceId: string
  ) => {
    const mockResult: NodeTestResult = {
      success: true,
      data: mockTestResult,
      timestamp: Date.now(),
      nodeId: nodeInstanceId
    };

    updateNodeTestResult(nodeInstanceId, mockResult);
    
    logger.info('保存模拟测试数据', { nodeInstanceId, mockTestResult });
    showSuccess('成功', '模拟测试数据已保存');
  }, [updateNodeTestResult, showSuccess]);

  /**
   * 清除节点测试结果
   */
  const clearNodeTestResult = useCallback((nodeInstanceId: string) => {
    updateNodeTestResult(nodeInstanceId, {
      success: false,
      error: '测试结果已清除',
      timestamp: Date.now(),
      nodeId: nodeInstanceId
    });

    logger.info('清除节点测试结果', { nodeInstanceId });
  }, [updateNodeTestResult]);

  /**
   * 批量测试多个节点
   */
  const handleBatchNodeTest = useCallback(async (nodeIds: string[]) => {
    const results = [];

    for (const nodeId of nodeIds) {
      const nodeDetails = nodesDetailsMap[nodeId];
      if (nodeDetails && nodeDetails.savedValues) {
        const result = await handleNodeTest(nodeDetails.savedValues, nodeId);
        results.push({ nodeId, result });
      }
    }

    const successCount = results.filter(r => r.result.success).length;
    const totalCount = results.length;

    if (successCount === totalCount) {
      showSuccess('成功', `所有节点测试成功 (${successCount}/${totalCount})`);
    } else if (successCount > 0) {
      showWarning('部分成功', `部分节点测试成功 (${successCount}/${totalCount})`);
    } else {
      showError('出错:', '所有节点测试失败');
    }

    return results;
  }, [nodesDetailsMap, handleNodeTest, showError, showSuccess, showWarning]);

  /**
   * 获取节点测试状态
   */
  const getNodeTestStatus = useCallback((nodeInstanceId: string) => {
    const isCurrentlyTesting = testingNodes.has(nodeInstanceId);
    const testResult = nodesTestResultsMap[nodeInstanceId];
    
    return {
      isTesting: isCurrentlyTesting,
      hasResult: !!testResult,
      isSuccess: testResult?.success || false,
      lastTestTime: testResult?.timestamp,
      error: testResult?.error,
      data: testResult?.data
    };
  }, [testingNodes, nodesTestResultsMap]);

  /**
   * 检查节点是否可以测试
   */
  const canTestNode = useCallback((nodeInstanceId: string) => {
    const nodeDetails = nodesDetailsMap[nodeInstanceId];
    if (!nodeDetails) return false;

    const previousNodeIds = getAllPreviousNodeIds(nodeInstanceId, edgesState, nodesDetailsMap);
    return canNodeExecute(nodeInstanceId, nodeDetails, previousNodeIds, nodesTestResultsMap);
  }, [nodesDetailsMap, edgesState, nodesTestResultsMap]);

  return {
    // 状态
    isTestingWorkflow,
    testingNodes,
    
    // 测试操作
    handleNodeTest,
    handleLeftPanelNodeTest,
    handleWorkflowTest,
    handleSaveMockData,
    handleBatchNodeTest,
    
    // 工具函数
    clearNodeTestResult,
    getNodeTestStatus,
    canTestNode,
    getLatestNodesTestResultsMap
  };
};