/**
 * useWorkflowOperations hook 测试
 */

import { renderHook, act } from '@testing-library/react';
import { useWorkflowOperations } from '../../hooks/useWorkflowOperations';

// Mock fetch
global.fetch = jest.fn();

// Mock数据
const mockProps = {
  workflowId: 'test-workflow-id',
  workflowName: '测试工作流',
  nodes: [],
  edges: [],
  nodesDetailsMap: {},
  currentCanvasNodes: [],
  currentCanvasEdges: [],
  showError: jest.fn(),
  showSuccess: jest.fn(),
  showWarning: jest.fn()
};

describe('useWorkflowOperations', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  it('应该初始化正确的状态', () => {
    const { result } = renderHook(() => useWorkflowOperations(mockProps));

    expect(result.current.isSaving).toBe(false);
    expect(result.current.isSharing).toBe(false);
    expect(typeof result.current.handleSave).toBe('function');
    expect(typeof result.current.handleShare).toBe('function');
  });

  it('应该处理保存操作', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: {} })
    });

    const { result } = renderHook(() => useWorkflowOperations(mockProps));

    await act(async () => {
      await result.current.handleSave();
    });

    expect(fetch).toHaveBeenCalledWith('/api/workflow-config', expect.objectContaining({
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: expect.stringContaining(mockProps.workflowId)
    }));

    expect(mockProps.showSuccess).toHaveBeenCalledWith('成功', '工作流保存成功');
  });

  it('应该处理保存失败', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error'
    });

    const { result } = renderHook(() => useWorkflowOperations(mockProps));

    await act(async () => {
      await result.current.handleSave();
    });

    expect(mockProps.showError).toHaveBeenCalledWith('出错:', expect.stringContaining('HTTP 500'));
  });

  it('应该处理缺少工作流ID的情况', async () => {
    const propsWithoutId = { ...mockProps, workflowId: '' };
    const { result } = renderHook(() => useWorkflowOperations(propsWithoutId));

    await act(async () => {
      await result.current.handleSave();
    });

    expect(mockProps.showError).toHaveBeenCalledWith('出错:', '工作流ID不存在，无法保存');
    expect(fetch).not.toHaveBeenCalled();
  });

  it('应该防止重复保存', async () => {
    (fetch as jest.Mock).mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: async () => ({ success: true, data: {} })
      }), 100))
    );

    const { result } = renderHook(() => useWorkflowOperations(mockProps));

    // 同时触发两次保存
    act(() => {
      result.current.handleSave();
      result.current.handleSave();
    });

    // 等待异步操作完成
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 150));
    });

    // 应该只调用一次fetch
    expect(fetch).toHaveBeenCalledTimes(1);
  });

  it('应该处理分享操作', async () => {
    const { result } = renderHook(() => useWorkflowOperations(mockProps));

    await act(async () => {
      await result.current.handleShare();
    });

    expect(mockProps.showSuccess).toHaveBeenCalledWith('成功', '工作流分享链接已生成');
  });

  it('应该处理重命名操作', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: {} })
    });

    const { result } = renderHook(() => useWorkflowOperations(mockProps));

    await act(async () => {
      await result.current.handleRename('新工作流名称');
    });

    expect(fetch).toHaveBeenCalledWith(
      `/api/workflow-config/${mockProps.workflowId}/rename`,
      expect.objectContaining({
        method: 'PATCH',
        body: JSON.stringify({ name: '新工作流名称' })
      })
    );

    expect(mockProps.showSuccess).toHaveBeenCalledWith('成功', '工作流重命名成功');
  });

  it('应该处理空名称的重命名', async () => {
    const { result } = renderHook(() => useWorkflowOperations(mockProps));

    await act(async () => {
      await result.current.handleRename('   ');
    });

    expect(mockProps.showWarning).toHaveBeenCalledWith('警告', '工作流名称不能为空');
    expect(fetch).not.toHaveBeenCalled();
  });
});