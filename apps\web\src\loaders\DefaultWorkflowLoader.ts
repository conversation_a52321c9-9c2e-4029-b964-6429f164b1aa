import { getAllWorkflowConfigs, getWorkflowConfigById, WorkflowConfig, WorkflowEngineConfig } from "@repo/db"
import { Workflow } from "@repo/engine";
import { IWorkflowLoader, WorkflowData, WorkflowListOptions } from "@repo/common";

export class DefaultWorkflowLoader implements IWorkflowLoader {

    async get(id: string): Promise<WorkflowData | null | undefined> {
        const config = await getWorkflowConfigById(id);
        if(!config) {
            return null;
        }

        const configData = {
            id: config.id,
            name: config.name,
            actions: config.nodesInfo ? JSON.parse(config.nodesInfo) : [],
            edges: config.relation ? JSON.parse(config.relation) : [],
        } as unknown as Workflow;

        return {
            id: config.id,
            config: configData
        } as unknown as WorkflowData;
    }

    async list(opts?: WorkflowListOptions): Promise<WorkflowData[] | null | undefined> {
        const configs : WorkflowData[] = [];
        const records = await getAllWorkflowConfigs(opts?.limit);
        for(let record of records) {
            const configData = {
                id: record.id,
                name: record.name,
                actions: record.nodesInfo ? JSON.parse(record.nodesInfo) : [],
                edges: record.relation ? JSON.parse(record.relation) : [],
            } as unknown as Workflow;
            configs.push({
                id: record.id,
                config: configData,
            } as unknown as WorkflowData);
        }
        return configs;
    }
}