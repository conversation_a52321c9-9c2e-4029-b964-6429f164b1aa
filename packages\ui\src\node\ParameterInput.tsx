"use client";

import React from 'react';
import { UnifiedParameterInput, type UnifiedParameterField } from '../components/forms/unified/UnifiedParameterInput';

interface ParameterInputProps {
  parameter: {
    name: string;
    displayName: string;
    type: string;
    controlType?: string;
    default?: any;
    options?: { name: string; value: any; description?: string }[];
    description?: string;
    placeholder?: string;
    ConnectType?: string; // 添加ConnectType支持
    displayOptions?: {
      show?: {
        [key: string]: string[];
      };
      hide?: {
        [key: string]: string[];
      };
    };
   };
  value: any;
  onChange: (name: string, value: any) => void;
  formValues?: Record<string, any>;
  onExpandModeChange?: (expanded: boolean) => void;
  connectConfigs?: Array<{
    id: string;
    name: string;
    ctype: string;
    nodeinfo: Record<string, any>;
    description?: string;
  }>; // 添加连接配置数据源
  onFetchConnectConfigs?: (ctype: string) => Promise<Array<{
    id: string;
    name: string;
    ctype: string;
    nodeinfo: Record<string, any>;
    description?: string;
  }>>; // 动态获取连接配置的回调
  onFetchTables?: (datasourceId: string) => Promise<{
    loading: boolean;
    error: string | null;
    tableOptions: Array<{ label: string; value: string; }>;
  }>; // 动态获取表名的回调
}

export const ParameterInput: React.FC<ParameterInputProps> = ({
  parameter,
  value,
  onChange,
  formValues,
  onExpandModeChange,
  connectConfigs,
  onFetchConnectConfigs,
  onFetchTables
}) => {
  console.log('🔧 [ParameterInput] 组件渲染:', {
    parameter: parameter.name,
    displayName: parameter.displayName,
    type: parameter.type,
    value,
    hasConnectConfigs: !!connectConfigs,
    connectConfigsLength: connectConfigs?.length || 0
  });
  // 将原始 parameter 转换为统一的 field 格式
  const unifiedField: UnifiedParameterField = {
    name: parameter.name,
    displayName: parameter.displayName,
    type: parameter.type,
    controlType: parameter.controlType,
    default: parameter.default,
    options: parameter.options,
    description: parameter.description,
    displayOptions: parameter.displayOptions,
    ConnectTypes: parameter.ConnectType, // 传递ConnectType
    // Node 变体的默认值
    hint: undefined,
    placeholder: parameter.placeholder, // 传递placeholder属性
    required: false,
    isSecure: false,
    typeOptions: undefined
  };

  console.log('🔧 [ParameterInput] 即将渲染 UnifiedParameterInput');
  
  return (
    <UnifiedParameterInput
      variant="node"
      field={unifiedField}
      value={value}
      onChange={onChange}
      formValues={formValues}
      onExpandModeChange={onExpandModeChange}
      connectConfigs={connectConfigs}
      onFetchConnectConfigs={onFetchConnectConfigs}
      onFetchTables={onFetchTables}
    />
  );
};