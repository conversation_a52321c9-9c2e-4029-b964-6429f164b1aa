/**
 * 边处理工具函数测试
 */

import { 
  createEdge,
  validateEdgeConnection,
  processEdgeSubflow,
  convertToWorkflowEdge,
  convertToSaveEdge,
  processEdgesForSave,
  findEdgesConnectedToNode,
  removeEdgesForNode,
  updateEdgeStyle,
  wouldCreateCycle,
  getEdgeLabel,
  updateEdgeLabels,
  EDGE_STYLE_CONFIG
} from '../../utils/edgeProcessing';

// 模拟数据
const mockConnection = {
  source: 'node1',
  target: 'node2',
  sourceHandle: 'right-0',
  targetHandle: 'left-0'
};

const mockEdges = [
  { 
    id: 'edge1', 
    source: 'node1', 
    target: 'node3',
    sourceHandle: 'right-0',
    targetHandle: 'left-0'
  },
  { 
    id: 'edge2', 
    source: 'node2', 
    target: 'node3',
    sourceHandle: 'right-0',
    targetHandle: 'left-0'
  },
  { 
    id: 'edge3', 
    source: 'node3', 
    target: 'node4',
    sourceHandle: 'right-0',
    targetHandle: 'left-0'
  }
];

const mockSourceNode = {
  id: 'node1',
  data: {
    link: {
      outputs: [
        { desc: '正常输出' },
        { desc: '循环', subflow: true }
      ]
    }
  }
};

describe('边处理工具函数', () => {
  describe('createEdge', () => {
    it('应该创建新的边', () => {
      const edge = createEdge(mockConnection);
      
      expect(edge.id).toBeDefined();
      expect(edge.source).toBe('node1');
      expect(edge.target).toBe('node2');
      expect(edge.sourceHandle).toBe('right-0');
      expect(edge.targetHandle).toBe('left-0');
      expect(edge.style).toBeDefined();
    });
  });

  describe('validateEdgeConnection', () => {
    it('应该验证有效的连接', () => {
      const result = validateEdgeConnection(mockConnection, []);
      
      expect(result.success).toBe(true);
    });

    it('应该检测到自连接', () => {
      const selfConnection = {
        source: 'node1',
        target: 'node1',
        sourceHandle: 'right-0',
        targetHandle: 'left-0'
      };
      
      const result = validateEdgeConnection(selfConnection, []);
      
      expect(result.success).toBe(false);
    });

    it('应该检测到重复连接', () => {
      const existingEdges = [
        { 
          id: 'existing', 
          source: 'node1', 
          target: 'node2',
          sourceHandle: 'right-0',
          targetHandle: 'left-0'
        }
      ];
      
      const result = validateEdgeConnection(mockConnection, existingEdges);
      
      expect(result.success).toBe(false);
    });
  });

  describe('processEdgeSubflow', () => {
    it('应该处理普通边', () => {
      const edge = { 
        id: 'edge1', 
        source: 'node1', 
        target: 'node2',
        sourceHandle: 'right-0'
      };
      
      const result = processEdgeSubflow(edge, mockSourceNode);
      
      expect(result.data).toBeUndefined();
    });

    it('应该处理循环子流程边', () => {
      const edge = { 
        id: 'edge1', 
        source: 'node1', 
        target: 'node2',
        sourceHandle: 'right-1' // 第二个输出是循环
      };
      
      const result = processEdgeSubflow(edge, mockSourceNode);
      
      expect(result.data?.subflow).toBe('loop');
    });
  });

  describe('findEdgesConnectedToNode', () => {
    it('应该找到连接到节点的所有边', () => {
      const result = findEdgesConnectedToNode('node3', mockEdges);
      
      expect(result.incoming.length).toBe(2);
      expect(result.outgoing.length).toBe(1);
      
      expect(result.incoming[0].source).toBe('node1');
      expect(result.incoming[1].source).toBe('node2');
      expect(result.outgoing[0].target).toBe('node4');
    });
  });

  describe('removeEdgesForNode', () => {
    it('应该移除与节点相关的所有边', () => {
      const result = removeEdgesForNode('node3', mockEdges);
      
      expect(result.length).toBe(0);
    });

    it('应该保留与节点无关的边', () => {
      const result = removeEdgesForNode('node2', mockEdges);
      
      expect(result.length).toBe(2);
      expect(result[0].id).toBe('edge1');
      expect(result[1].id).toBe('edge3');
    });
  });

  describe('wouldCreateCycle', () => {
    it('应该检测到会形成循环的连接', () => {
      const cycleConnection = {
        source: 'node4',
        target: 'node1',
        sourceHandle: 'right-0',
        targetHandle: 'left-0'
      };
      
      const result = wouldCreateCycle(cycleConnection, mockEdges);
      
      expect(result).toBe(true);
    });

    it('应该允许不会形成循环的连接', () => {
      const safeConnection = {
        source: 'node1',
        target: 'node4',
        sourceHandle: 'right-0',
        targetHandle: 'left-0'
      };
      
      const result = wouldCreateCycle(safeConnection, mockEdges);
      
      expect(result).toBe(false);
    });
  });
});