/**
 * 重构后的工作流页面 - 组件组装器
 * 
 * 这个文件将原来2000+行的单体文件重构为简洁的组件组装器
 * 主要职责：
 * 1. 组装各个功能组件
 * 2. 管理全局状态和数据流
 * 3. 提供错误边界和加载状态
 * 4. 协调组件间的通信
 */

"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { ReactFlowProvider } from 'reactflow';

// UI组件导入
import { ContentArea, TabsContainer, Tab, ToastManager, useToast } from '@repo/ui';

// 应用层组件导入
import { WorkflowHeader } from '@repo/ui/main/flow';
import { TestButtonContainer } from '@repo/ui';
import { CoButton } from '@repo/ui/src/components/basic/Buttons';
import { MdNotStarted } from "react-icons/md";

// 导入重构后的工作流编辑器组件
import { WorkflowEditor } from './components/WorkflowEditor/WorkflowEditor';

// Context导入
import { useWorkflow } from '@/contexts/WorkflowContext';

// Hooks导入
import { useWorkflowExport } from './hooks/useWorkflowExport';
import { useWorkflowOperations } from './hooks/useWorkflowOperations';
import { useNodeTesting } from './hooks/useNodeTesting';

// 样式导入
import 'reactflow/dist/style.css';

// 连接配置和表名获取函数 要删----
const fetchConnectConfigs = async (ctype?: string) => {
  try {
    const url = ctype ? `/api/connect-configs?ctype=${ctype}` : '/api/connect-configs';
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Failed to fetch connect configs');
    }
    const data = await response.json();
    return data.dataSource || [];
  } catch (error) {
    console.error('获取连接配置失败:', error);
    return [];
  }
};

const fetchTables = async (datasourceId: string, search?: string) => {
  try {
    const url = `/api/tables/${datasourceId}${search ? `?search=${search}` : ''}`;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Failed to fetch tables');
    }
    const data = await response.json();
    return {
      loading: false,
      error: null,
      tableOptions: data.tableOptions || []
    };
  } catch (error) {
    console.error('获取表名失败:', error);
    return {
      loading: false,
      error: error instanceof Error ? error.message : '获取表名失败',
      tableOptions: []
    };
  }
};

/**
 * 工作流页面内容组件
 */
const WorkflowPageContent: React.FC = () => {
  // 页面状态
  const [isActive, setIsActive] = useState(false);
  const [activeTab, setActiveTab] = useState('editor');
  const [menuCollapsed, setMenuCollapsed] = useState(false);

  // 工作流上下文
  const {
    nodes,
    edges,
    workflowId,
    workflowName,
    setWorkflowName,
    nodesDetailsMap,
    nodesTestResultsMap,
    updateNodeTestResult,
    updateNodeDetails,
    setNodes,
    setEdges
  } = useWorkflow();

  // 保存当前画布的最新状态
  const [currentCanvasNodes, setCurrentCanvasNodes] = useState<any[]>([]);
  const [currentCanvasEdges, setCurrentCanvasEdges] = useState<any[]>([]);

  /**
   * 处理画布状态变化
   */
  const handleCanvasStateChange = useCallback((updatedNodes: any[], updatedEdges: any[]) => {
    // 保存最新的画布状态
    setCurrentCanvasNodes(updatedNodes);
    setCurrentCanvasEdges(updatedEdges);
  }, []);

  // Toast管理
  const {
    toasts,
    removeToast,
    showError,
    showSuccess,
    showWarning
  } = useToast();

  // 工作流导出功能
  const {
    handleCopyToClipboard,
    handleExportJSON
  } = useWorkflowExport({
    workflowId: workflowId || '',
    workflowName: workflowName || '',
    nodes,
    edges,
    nodesDetailsMap,
    currentCanvasNodes,
    currentCanvasEdges,
    showError,
    showSuccess,
    showWarning
  });

  // 工作流操作功能
  const {
    handleSave,
    handleShare,
    handleMoreOptions,
    handleAddTag,
    isSaving
  } = useWorkflowOperations({
    workflowId: workflowId || '',
    workflowName: workflowName || '',
    nodes,
    edges,
    nodesDetailsMap,
    currentCanvasNodes,
    currentCanvasEdges,
    showError,
    showSuccess,
    showWarning
  });

  // 节点测试功能
  const {
    handleWorkflowTest,
    isTestingWorkflow
  } = useNodeTesting({
    workflowId: workflowId || '',
    nodesDetailsMap,
    nodesTestResultsMap,
    edgesState: edges,
    updateNodeTestResult,
    updateNodeDetails,
    showError,
    showSuccess,
    showWarning
  });

  /**
   * 处理菜单折叠状态变化
   */
  const handleMenuCollapseChange = useCallback((collapsed: boolean) => {
    setMenuCollapsed(collapsed);
  }, []);

  /**
   * 处理标签页切换
   */
  const handleEditorTabClick = () => {
    setActiveTab('editor');
  };

  const handleExecutionsTabClick = () => {
    setActiveTab('executions');
  };

  return (
    <>
      {/* 工作流头部 */}
      <WorkflowHeader
        workflowId={workflowId || 'errorWorkflowId'}
        workflowName={workflowName}
        onWorkflowNameChange={setWorkflowName}
        isActive={isActive}
        onActiveChange={setIsActive}
        onSave={handleSave}
        onShare={handleShare}
        onMoreOptions={handleMoreOptions}
        onAddTag={handleAddTag}
        onCopyToClipboard={handleCopyToClipboard}
        onExportJSON={handleExportJSON}
      />

      {/* 标签页导航 */}
      <TabsContainer>
        <Tab $active={activeTab === 'editor'} onClick={handleEditorTabClick}>
          业务设计
        </Tab>
        <Tab $active={activeTab === 'executions'} onClick={handleExecutionsTabClick}>
          执行记录
        </Tab>
      </TabsContainer>

      {/* 主要内容区域 */}
      <ContentArea style={{ 
        padding: 0, 
        display: 'flex', 
        flexDirection: 'column', 
        height: 'calc(100vh - 110px)' 
      }}>
        {activeTab === 'editor' ? (
          <ReactFlowProvider>
            <WorkflowEditor
              onMenuCollapseChange={handleMenuCollapseChange}
              showError={showError}
              showWarning={showWarning}
              onCanvasStateChange={handleCanvasStateChange}
              onFetchConnectConfigs={fetchConnectConfigs}
              onFetchTables={fetchTables}
            />
          </ReactFlowProvider>
        ) : (
          <div style={{ padding: '20px', width: '100%' }}>
            <h2>执行记录</h2>
            <p>工作流执行历史记录将在这里显示。</p>
          </div>
        )}
      </ContentArea>

      {/* 测试按钮 */}
      <TestButtonContainer $menuCollapsed={menuCollapsed}>
        <CoButton
          onClick={handleWorkflowTest}
          disabled={isTestingWorkflow || !workflowId}
        >
          <MdNotStarted size={16} />
          {isTestingWorkflow ? '测试中...' : '业务流测试'}
        </CoButton>
      </TestButtonContainer>

      {/* Toast管理器 */}
      <ToastManager toasts={toasts} onRemove={removeToast} />
    </>
  );
};

/**
 * 主工作流页面组件
 */
const WorkflowPage: React.FC = () => {
  return <WorkflowPageContent />;
};

export default WorkflowPage;