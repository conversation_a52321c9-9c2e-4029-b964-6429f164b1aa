import triggerData from "./WorkflowTrigger.json"
import schedulerData from "./WorkflowScheduler.json"

export * from "./client"
export * from "./workflow"
export * from "./agentic"
export * from "./FunctionManager"
export * from "./ActionManager"
export * from "./McpInterfaces"
export * from "./McpManager"
export * from "./AgentInterfaces"
export * from "./AgentManager"
export * from "./WorkflowRegister"
export * from "./EventMediator"

export const triggerConfig = {
    ...triggerData
}

export const schedulerConfig = {
    ...schedulerData
}