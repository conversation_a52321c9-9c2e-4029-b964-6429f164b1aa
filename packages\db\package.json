{"name": "@repo/db", "version": "0.0.0", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**"], "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./prisma": {"import": "./dist/prisma.js", "require": "./dist/prisma.js", "types": "./dist/prisma.d.ts"}, "./prisma/client": {"import": "./dist/prisma-client.js", "require": "./dist/prisma-client.js", "types": "./dist/prisma-client.d.ts"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "check-types": "tsc --noEmit", "clean": "powershell -Command \"Remove-Item -Path '.turbo', 'node_modules', 'dist' -Recurse -Force -ErrorAction SilentlyContinue\"", "db:generate": "prisma generate", "db:push": "prisma db push --skip-generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset --force", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.11.0", "bcryptjs": "^2.4.3"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/bcryptjs": "^2.4.6", "eslint": "^9.25.0", "prisma": "^5.11.0", "tsup": "^8.0.2", "typescript": "^5.8.2"}}