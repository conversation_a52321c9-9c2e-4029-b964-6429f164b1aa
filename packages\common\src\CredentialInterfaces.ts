import { DatabaseType } from "./ConnectInterfaces";

export const CredentialDef = {
    identifier: "ICredentialLoader"
}

export interface CredentialData {
    id: string;
    config?: any;
}

export interface CredentialListOptions {
    type?: DatabaseType;
}

export interface ICredentialLoader {
    get(id: string): Promise<CredentialData | null | undefined>;
    list(opts?: CredentialListOptions) : Promise<CredentialData[] | null | undefined>;
}