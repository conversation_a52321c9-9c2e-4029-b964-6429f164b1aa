import styled , { createGlobalStyle }  from 'styled-components';

// 主容器 - 左右布局
export const ChatDisplayContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #0f1b3a;
`;

// 左侧历史区域
export const HistoryPanel = styled.div<{ $isVisible: boolean; $width: number }>`
  width: ${props => props.$isVisible ? props.$width : 0}px;
  height: 100%;
  background-color: #0a1428;
  border-right: ${props => props.$isVisible ? '1px solid rgba(255, 255, 255, 0.1)' : 'none'};
  overflow: hidden;
  transition: ${props => props.$isVisible ? 'width 0.2s ease' : 'none'};
  flex-shrink: 0;
`;

// 右侧聊天区域
export const ChatPanel = styled.div`
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

// Styled Components
export const ChatContainer = styled.div<{ $theme: string }>`
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: ${props => props.$theme === 'light' ? '#333' : '#fff'};
  background-color: ${props => props.$theme === 'light' ? '#fff' : '#0f1b3a'};
`

export const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`

export const MessageContainer = styled.div<{ $role: string }>`
  display: flex;
  flex-direction: column;
  align-items: ${props => props.$role === 'user' ? 'flex-end' : 'flex-start'};
  max-width: 85%;
  ${props => props.$role === 'user' ? 'margin-left: auto;' : 'margin-right: auto;'}
`

export const MessageHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
`

export const RoleBadge = styled.span<{ $role: string }>`
  background: ${props => props.$role === 'user' ? '#007bff' : '#6c757d'};
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
`

export const Timestamp = styled.span`
  font-size: 12px;
  color: #6c757d;
`

export const MessageContent = styled.div<{ $theme?: string; $hasThinkContent?: boolean; $isMine?: boolean }>`

  background: ${props => props.$theme === 'light' ? '#f8f9fa' : 'rgba(255, 255, 255, 0.05)'};
  padding: 12px 16px;
  border-radius: 12px;
  max-width: 100%;
  
  p, main, section  {
    margin: 0 0 8px 0;
    &:last-child {
      margin-bottom: 0;
    };
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    font-size: 13px;
    font-weight: 200;
  }

  ${props => !props.$isMine && !props.$hasThinkContent && `
    p {
      color:rgb(196, 195, 195);
      font-size:12px;
    }
  `}

  ul, ol {
    margin: 8px 0;
    padding-left: 20px;
    font-size:13px;
  }
  
  h1, h2, h3, h4, h5, h6 {
    margin: 16px 0 8px 0;
    &:first-child {
      margin-top: 0;
    }
  }
`

export const CodeBlockContainer = styled.div`
  margin: 16px 0;
  border-radius: 4px;
  overflow: hidden;  
  background: rgba(7, 79, 138);
  /* Ensure this container is not treated as a paragraph */
  display: block;
`

export const CodeBlockHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background:rgba(7, 79, 138, 0.66);
`

export const LanguageLabel = styled.span`
  font-size: 12px;
  font-weight: 600;
  color: #cfcfcfff;
`

export const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`

export const ToolButton = styled.button`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: #ededed;
  font-size: 12px;
  transition: all 0.2s;
  
  &:hover {
    background: #dee2e6;
    color: #495057;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`

export const CodeBlockContent = styled.div<{ $wrapped: boolean }>`
  // max-height: ${props => props.$wrapped ? 'none' : '400px'};
  // overflow: auto;
`

export const InlineCode = styled.code`
  background: #f1f3f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: Monaco, Consolas, 'Courier New', monospace;
  font-size: 0.9em;
  color: #d63384;
`

export const ImageContainer = styled.div`
  margin: 16px 0;
  text-align: center;
`

export const Blockquote = styled.blockquote`
  border-left: 4px solid #007bff;
  margin: 16px 0;
  padding-left: 16px;
  color: #6c757d;
  font-style: italic;
`

export const TableContainer = styled.div`
  overflow-x: auto;
  margin: 16px 0;  
  table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      border: 1px solid #dee2e6;
      padding: 8px 12px;
      text-align: left;
    }
    
    th {
      background: #f8f9fa;
      font-weight: 600;
    }
  }
`

export const InputContainer = styled.div`
  padding: 16px;
  border-top: 1px solid #e1e5e9;
  background: #f8f9fa;
`

export const InputForm = styled.form`
  display: flex;
  gap: 8px;
  align-items: flex-end;
`

export const TextArea = styled.textarea`
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-family: inherit;
  font-size: 13px;
  resize: vertical;
  outline: none;
  transition: border-color 0.2s;
  
  &:focus {
    border-color: #007bff;
  }
  
  &::placeholder {
    color: #6c757d;
  }
`

export const SendButton = styled.button`
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover:not(:disabled) {
    background: #0056b3;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`

export const TypingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  
  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.4s; }
  }
  
  @keyframes typing {
    0%, 80%, 100% { opacity: 0.5; }
    40% { opacity: 1; }
  }
`

export const ThinkingBlock = styled.div`
  // background-color: #f5f5f5;
  // border-left: 3px solid #ccc;
  // padding: 12px 15px;
  // margin: 15px 0;
  // border-radius: 6px;
  // font-style: italic;
  position: relative;
  // overflow: hidden;
  margin-bottom:14px;
  color:rgb(196, 195, 195);
  p{
      font-size: 12px;
      // color:rgb(196, 195, 195);
  }
  &::before {
    content: '思考过程';
    position: absolute;
    top: -23px;
    right: 0;
    background:rgba(245, 245, 245, 0.21);
    color: #ededed70;
    font-size: 0.75em;
    padding: 2px 8px;
    //border-bottom-left-radius: 4px;
    font-style: normal;
  }
`

// 为原始 HTML think 标签添加全局样式
// export const GlobalThinkStyles = createGlobalStyle`
//   .thinking-block code {
//     background-color: rgba(200, 200, 200, 0.2) !important;
//     color: inherit !important;
//   }
  
//   .thinking-block pre {
//     background-color: rgba(0, 0, 0, 0.1) !important;
//     border: 1px solid rgba(255, 255, 255, 0.1) !important;
//   }
// `