"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { CONTROL_STYLES } from './common-styles';

interface SelectFilterOption {
  value: string | number;
  label: string;
}

interface SelectFilterProps {
  options?: SelectFilterOption[]; // 静态选项，可选
  value?: string | number;
  onChange?: (value: string | number) => void;
  placeholder?: string;
  filterPlaceholder?: string;
  style?: React.CSSProperties;
  // 动态加载相关props
  datasourceId?: string; // 数据源ID，用于触发数据加载
  onLoadOptions?: (datasourceId: string, search?: string) => Promise<SelectFilterOption[]>; // 动态加载回调
  enableDynamicLoad?: boolean; // 是否启用动态加载
  // 新增：直接传递表名数据
  tableOptions?: SelectFilterOption[]; // 表名选项，由上层传递
  loading?: boolean; // 加载状态，由上层控制
  error?: string | null; // 错误状态，由上层控制
  onFetchTables?: (datasourceId: string) => Promise<{
    loading: boolean;
    error: string | null;
    tableOptions: Array<{ label: string; value: string; }>;
  }>; // 动态获取表名的回调
}

const SelectFilterContainer = styled.div`
  position: relative;
  width: 100%;
`;

const SelectFilterInput = styled.div`
  width: 100%;
  padding: 8px;
  border: 1px solid ${props => props.theme?.colors?.border || '#e5e7eb'};
  border-radius: 4px;
  font-size: 14px;
  color: ${props => props.theme?.colors?.textPrimary || '#374151'};
  background: ${props => props.theme?.colors?.inputBg || '#ffffff'};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  outline: none;
  box-sizing: border-box;
  
  &:hover {
    border-color: ${props => props.theme?.colors?.borderHover || '#d1d5db'};
  }

  &:focus {
    border-color: ${props => props.theme?.colors?.accent || '#3b82f6'};
    outline: none;
  }

  .placeholder {
    color: ${props => props.theme?.colors?.textTertiary || '#9ca3af'};
    font-size: 14px;
  }
`;

const DropdownContainer = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: ${props => props.theme?.colors?.inputBg || '#ffffff'};
  border: 1px solid ${props => props.theme?.colors?.border || '#e5e7eb'};
  border-radius: 4px;
  margin-top: 2px;
  max-height: 200px;
  overflow-y: auto;
  display: ${props => props.$isOpen ? 'block' : 'none'};
  z-index: 1000;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
`;

const FilterInput = styled.input`
  width: 100%;
  padding: 8px;
  border: none;
  border-bottom: 1px solid ${props => props.theme?.colors?.border || '#e5e7eb'};
  outline: none;
  font-size: 14px;
  color: ${props => props.theme?.colors?.textPrimary || '#374151'};
  background: ${props => props.theme?.colors?.inputBg || '#ffffff'};
  box-sizing: border-box;

  &::placeholder {
    color: ${props => props.theme?.colors?.textTertiary || '#9ca3af'};
    font-size: 14px;
  }
`;

const Option = styled.div<{ $isSelected?: boolean }>`
  padding: 8px;
  cursor: pointer;
  font-size: 14px;
  color: ${props => props.$isSelected ? '#ffffff' : (props.theme?.colors?.textPrimary || '#374151')};
  background: ${props => props.$isSelected ? (props.theme?.colors?.accent || '#3b82f6') : 'transparent'};
  transition: all 0.15s ease;
  box-sizing: border-box;

  &:hover {
    background: ${props => props.$isSelected ? (props.theme?.colors?.accent || '#3b82f6') : '#f9fafb'};
  }
`;

const NoResults = styled.div`
  padding: 8px;
  color: ${props => props.theme?.colors?.textSecondary || '#6b7280'};
  font-size: 14px;
  text-align: center;
  box-sizing: border-box;
`;

const LoadingMessage = styled.div`
  padding: 8px;
  color: ${props => props.theme?.colors?.textSecondary || '#6b7280'};
  font-size: 14px;
  text-align: center;
  box-sizing: border-box;
`;

const ErrorMessage = styled.div`
  padding: 8px;
  color: ${props => props.theme?.colors?.error || '#ef4444'};
  font-size: 14px;
  text-align: center;
  box-sizing: border-box;
`;

// 默认的动态加载函数（保留向后兼容性）
const defaultLoadOptions = async (datasourceId: string, search?: string): Promise<SelectFilterOption[]> => {
  console.warn('SelectFilter: 直接API调用已废弃，请通过props传递数据');
  return [];
};

export const SelectFilter: React.FC<SelectFilterProps> = ({
  options = [],
  value,
  onChange,
  placeholder = '请选择',
  filterPlaceholder = '输入过滤条件...',
  style,
  datasourceId,
  onLoadOptions = defaultLoadOptions,
  enableDynamicLoad = false,
  tableOptions = [],
  loading = false,
  error = null,
  onFetchTables
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [dynamicOptions, setDynamicOptions] = useState<SelectFilterOption[]>([]);
  const [internalLoading, setInternalLoading] = useState(false);
  const [internalError, setInternalError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const filterInputRef = useRef<HTMLInputElement>(null);
  
  // 确定当前使用的选项列表
  // 优先级：tableOptions > dynamicOptions > options
  const currentOptions = tableOptions.length > 0 ? tableOptions : 
                        (enableDynamicLoad ? dynamicOptions : options);
  
  // 确定当前的加载和错误状态
  // 如果有外部传递的状态，优先使用外部状态
  const currentLoading = tableOptions.length > 0 ? loading : internalLoading;
  const currentError = tableOptions.length > 0 ? error : internalError;
  
  // 当前选中的选项
  // 如果在当前选项中找不到，但有值，则创建一个临时选项用于显示
  const selectedOption = currentOptions.find(opt => opt.value === value) || 
    (value ? { value, label: value } : undefined);
  
  // 过滤后的选项
  const filteredOptions = currentOptions.filter(opt => 
    opt.label.toLowerCase().includes(filterText.toLowerCase())
  );

  // 动态加载数据的函数（仅在没有外部tableOptions时使用）
  const loadOptions = useCallback(async (search?: string) => {
    // 如果有外部传递的tableOptions，则不执行内部加载
    if (tableOptions.length > 0) {
      return;
    }
    
    if (!enableDynamicLoad || !datasourceId) {
      if (enableDynamicLoad && !datasourceId) {
        setInternalError('未指定数据源');
      }
      return;
    }

    setInternalLoading(true);
    setInternalError(null);
    
    try {
      // 优先使用新的onFetchTables回调
      if (onFetchTables) {
        const result = await onFetchTables(datasourceId);
        setDynamicOptions(result.tableOptions);
        if (result.error) {
          setInternalError(result.error);
        }
      } else {
        // 回退到原有的onLoadOptions
        const loadedOptions = await onLoadOptions(datasourceId, search);
        setDynamicOptions(loadedOptions);
      }
    } catch (err) {
      setInternalError(err instanceof Error ? err.message : '加载数据失败');
      setDynamicOptions([]);
    } finally {
      setInternalLoading(false);
    }
  }, [enableDynamicLoad, datasourceId, onLoadOptions, onFetchTables, tableOptions.length]);

  // 当数据源ID变化时，清空之前的数据（仅在没有外部tableOptions时）
  useEffect(() => {
    if (tableOptions.length > 0) {
      return; // 有外部数据时，不处理内部状态
    }
    
    if (enableDynamicLoad) {
      if (datasourceId) {
        // 有数据源时，清空之前的数据和错误状态
        setDynamicOptions([]);
        setInternalError(null);
      } else {
        // 没有数据源时，清空数据并设置错误
        setDynamicOptions([]);
        setInternalError('请先选择数据源');
      }
    }
  }, [enableDynamicLoad, datasourceId, tableOptions.length]);

  // 当下拉框打开且启用动态加载时，加载数据（仅在没有外部tableOptions时）
  useEffect(() => {
    if (tableOptions.length > 0) {
      return; // 有外部数据时，不执行内部加载
    }
    
    if (enableDynamicLoad && datasourceId && isOpen && dynamicOptions.length === 0 && !internalLoading) {
      loadOptions();
    }
  }, [isOpen, enableDynamicLoad, datasourceId, dynamicOptions.length, internalLoading, loadOptions, tableOptions.length]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFilterText(''); // 关闭时清空过滤文本
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 当下拉框打开时，自动聚焦到过滤输入框
  useEffect(() => {
    if (isOpen && filterInputRef.current) {
      setTimeout(() => {
        filterInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setFilterText(''); // 打开时清空过滤文本
    }
  };

  const handleSelect = (option: SelectFilterOption) => {
    onChange?.(option.value);
    setIsOpen(false);
    setFilterText('');
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFilterText = e.target.value;
    setFilterText(newFilterText);
    
    // 动态加载模式下，只在前端进行过滤，不再请求接口
    // 数据已经在下拉框打开时一次性加载完成
  };

  const handleFilterKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && filteredOptions.length > 0) {
      // 回车选择第一个匹配项
      handleSelect(filteredOptions?.[0]!);
    } else if (e.key === 'Escape') {
      // ESC 关闭下拉框
      setIsOpen(false);
      setFilterText('');
    }
  };

  return (
    <SelectFilterContainer ref={containerRef} style={style}>
      <SelectFilterInput onClick={handleToggle} tabIndex={0}>
        {selectedOption ? (
          <span>{selectedOption.label}</span>
        ) : (
          <span className="placeholder">{placeholder}</span>
        )}
        <span>{isOpen ? '▲' : '▼'}</span>
      </SelectFilterInput>
      
      <DropdownContainer $isOpen={isOpen}>
        <FilterInput
          ref={filterInputRef}
          value={filterText}
          onChange={handleFilterChange}
          onKeyDown={handleFilterKeyDown}
          placeholder={filterPlaceholder}
          onClick={e => e.stopPropagation()}
        />
        {currentLoading ? (
          <LoadingMessage>正在加载数据...</LoadingMessage>
        ) : currentError ? (
          <ErrorMessage>{currentError}</ErrorMessage>
        ) : filteredOptions.length > 0 ? (
          filteredOptions.map(option => (
            <Option
              key={option.value}
              $isSelected={option.value === value}
              onClick={() => handleSelect(option)}
            >
              {option.label}
            </Option>
          ))
        ) : (
          <NoResults>{enableDynamicLoad ? '没有找到匹配的数据' : '没有匹配的选项'}</NoResults>
        )}
      </DropdownContainer>
    </SelectFilterContainer>
  );
};