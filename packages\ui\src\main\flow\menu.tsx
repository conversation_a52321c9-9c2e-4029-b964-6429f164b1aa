"use client";

import React, { useState, useMemo, useCallback } from 'react';

import {
  MenuContainer,
  MenuHeader,
  CollapseFlag,
  SearchContainer,
  NodeMenuSearch,
  MenuContent,
  CategoryHeader,
  CategoryHeaderContent,
  CategoryContent,
  CategoryIcon,
  CategoryTitle,
  EmptyState, 
  CategoryText,
  CategoryDescription,
  ExpandIcon,
  NodeItem,
  NodeContent,
  NodeIconContainer,
  NodeIcon,
  NodeTextContent,
  NodeTitle,
  NodeDescription,
} from './nodes';

import { ICategory, INodeBasic, Icon } from '@repo/common';

export interface ICategoryAndNode extends ICategory {
  nodes: INodeBasic[];
}

export interface IWorkflowMenuProps {
  dataSource: ICategoryAndNode[];
  onMenuCollapseChange?: (collapsed: boolean) => void;
}


// 优化的 Workflow menu 组件 - 使用React.memo防止不必要的重新渲染
export const WorkflowMenu: React.FC<IWorkflowMenuProps> = React.memo(({
  dataSource,
  onMenuCollapseChange
}) => {
  // 🏷️ 调试标识：优化版 menu
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['trigger']);
  const [menuCollapsed, setMenuCollapsed] = useState(false);

  // 优化：使用 useCallback 避免不必要的重新渲染
  const handleMenuCollapse = useCallback((collapsed: boolean) => {
    setMenuCollapsed(collapsed);
    onMenuCollapseChange?.(collapsed);
  }, [onMenuCollapseChange]);

  const toggleCategory = useCallback((categoryId: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  }, []);

  // 优化：缓存转换后的数据
  const categories = useMemo((): ICategoryAndNode[] => {
    if (!dataSource) return [];

    return dataSource.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      icon: category.icon,
      nodes: category.nodes.map(node => ({
        kind: node.kind,
        name: node.name,
        description: node.description,
        icon: `/nodes/${node.kind}/${node.kind}.svg` as Icon,
        categories: node.categories,
        nodeWidth: node.nodeWidth,
        version: node.version,
        link: node.link, // 添加 link 信息
      }))
    }));
  }, [dataSource]);

  // 优化：缓存拖拽处理函数
  const handleDragStart = useCallback((event: React.DragEvent<HTMLDivElement>, nodeData: INodeBasic) => {
    try {
      console.log("🚀 Drag start - nodeData:", nodeData);
      
      const serializableNodeData = {
        id: nodeData.kind,
        name: nodeData.name,
        description: nodeData.description,
        category: nodeData.categories?.[0] || 'default',
        categories: nodeData.categories || [], // 添加完整的categories信息用于触发器检查
        icon: nodeData.icon || '',
        version: nodeData.version,
        kind: nodeData.kind,
        nodeWidth: nodeData.nodeWidth,
        link: nodeData.link, // 添加 link 信息
        type: nodeData.categories?.[0] === 'trigger' ? 'triggerNode' :
          nodeData.categories?.[0] === 'AI' ? 'AI' : 'actionNode'
      };

      // 设置拖拽数据
      const dataString = JSON.stringify(serializableNodeData);
      event.dataTransfer.setData('application/reactflow', dataString);
      event.dataTransfer.effectAllowed = 'move';
      
      // 创建拖拽预览
      const dragPreview = document.createElement('div');
      dragPreview.style.cssText = `
        position: absolute;
        top: -1000px;
        left: -1000px;
        padding: 12px 16px;
        background: white;
        border: 2px solid #33C2EE;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        font-weight: 500;
        color: #333;
        z-index: 9999;
      `;
      
      dragPreview.innerHTML = `
        <img src="${nodeData.icon}" style="width: 20px; height: 20px;" alt="${nodeData.name}" />
        <span>${nodeData.name}</span>
      `;
      
      document.body.appendChild(dragPreview);
      
      // 设置拖拽图像
      event.dataTransfer.setDragImage(dragPreview, 50, 20);

      // 延迟移除，确保拖拽图像设置完成
      setTimeout(() => {
        if (document.body.contains(dragPreview)) {
          document.body.removeChild(dragPreview);
        }
      }, 100);
      
      console.log("🎨 Drag preview created and cleanup scheduled");
    } catch (err) {
      console.error('❌ 拖拽初始化错误:', err);
    }
  }, []);

  // 优化：搜索过滤逻辑
  const filteredCategories = useMemo(() => {
    if (!searchTerm) return categories;

    return categories.map(category => ({
      ...category,
      nodes: category.nodes.filter(
        node =>
          node.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          node.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    })).filter(category => category.nodes.length > 0);
  }, [categories, searchTerm]);

  // 折叠状态
  if (menuCollapsed) {
    return (
      <div>
        <MenuHeader onClick={() => handleMenuCollapse(false)} $collapsed={false}>
          <CollapseFlag>⛊⛊⛊⛊⛊⛊⛊⛊⛊⛊⛊⛊</CollapseFlag>
        </MenuHeader>
        <MenuContainer $collapsed={true} />
      </div>
    );
  }

  // 空状态处理
  if (!dataSource) {
    return (
      <MenuContainer $collapsed={false}>
        <EmptyState>正在加载节点数据...</EmptyState>
      </MenuContainer>
    );
  }

  if (categories.length === 0) {
    return (
      <MenuContainer $collapsed={false}>
        <EmptyState>正在加载节点分类...</EmptyState>
      </MenuContainer>
    );
  }

  return (
    <div>
      <MenuHeader onClick={() => handleMenuCollapse(true)} $collapsed={true}>
        <CollapseFlag>•••••••</CollapseFlag>
      </MenuHeader>
      <MenuContainer $collapsed={false}>
        <SearchContainer>
          <NodeMenuSearch
            type="text"
            placeholder="🔍︎ 查找节点"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>

        <MenuContent>
          {filteredCategories.map((category) => (
            <div key={category.id}>
              <CategoryHeader
                $expanded={expandedCategories.includes(category.id)}
                onClick={() => toggleCategory(category.id)}
              >
                <CategoryHeaderContent>
                  <CategoryIcon
                    dangerouslySetInnerHTML={{ __html: category.icon }}
                  />
                  <CategoryText>
                    <CategoryTitle>{category.name}</CategoryTitle>
                    <CategoryDescription>{category.description}</CategoryDescription>
                  </CategoryText>
                </CategoryHeaderContent>
                <ExpandIcon $expanded={expandedCategories.includes(category.id)}>
                  {/* <RightOutlined /> */}
                </ExpandIcon>
              </CategoryHeader>

              <CategoryContent $expanded={expandedCategories.includes(category.id)}>
                {category.nodes.map((node, index) => (
                  <NodeItem
                    key={`${category.id}-${node.kind}-${index}`}
                    draggable
                    onDragStart={(event) => handleDragStart(event, node)}
                    title="拖拽到画布添加节点"
                  >
                    <NodeContent>
                      <NodeIconContainer>
                        <NodeIcon src={node.icon as string} alt={node.name} />
                      </NodeIconContainer>
                      <NodeTextContent>
                        <NodeTitle>{node.name}</NodeTitle>
                        <NodeDescription>{node.description}</NodeDescription>
                      </NodeTextContent>
                    </NodeContent>
                  </NodeItem>
                ))}
              </CategoryContent>
            </div>
          ))}
        </MenuContent>
      </MenuContainer>
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数 - 深度比较dataSource数组
  if (prevProps.onMenuCollapseChange !== nextProps.onMenuCollapseChange) {
    return false;
  }

  // 比较dataSource数组长度
  if (prevProps.dataSource?.length !== nextProps.dataSource?.length) {
    return false;
  }

  // 如果都为空或null，认为相等
  if (!prevProps.dataSource && !nextProps.dataSource) {
    return true;
  }

  // 如果其中一个为空，认为不相等
  if (!prevProps.dataSource || !nextProps.dataSource) {
    return false;
  }

  // 深度比较每个category
  for (let i = 0; i < prevProps.dataSource.length; i++) {
    const prevCategory = prevProps.dataSource[i];
    const nextCategory = nextProps.dataSource[i];

    // 添加安全检查
    if (!prevCategory || !nextCategory) {
      return false;
    }

    if (prevCategory.id !== nextCategory.id ||
        prevCategory.name !== nextCategory.name ||
        prevCategory.description !== nextCategory.description ||
        prevCategory.icon !== nextCategory.icon ||
        prevCategory.nodes?.length !== nextCategory.nodes?.length) {
      return false;
    }

    // 比较nodes数组
    if (prevCategory.nodes && nextCategory.nodes) {
      for (let j = 0; j < prevCategory.nodes.length; j++) {
        const prevNode = prevCategory.nodes[j];
        const nextNode = nextCategory.nodes[j];

        // 添加安全检查
        if (!prevNode || !nextNode) {
          return false;
        }

        if (prevNode.kind !== nextNode.kind ||
            prevNode.name !== nextNode.name ||
            prevNode.description !== nextNode.description ||
            prevNode.icon !== nextNode.icon ||
            prevNode.version !== nextNode.version) {
          return false;
        }
      }
    }
  }

  return true;
});

// 设置displayName以便调试
WorkflowMenu.displayName = 'WorkflowMenu';